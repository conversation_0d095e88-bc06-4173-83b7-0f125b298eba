// Tool function interfaces and types

import type { DrawingPoint, DrawingPath, ToolSettings, CanvasBounds } from './index'

/**
 * Canvas tool functions interface
 */
export interface CanvasToolFunctions {
  initializeCanvas: (canvas: HTMLCanvasElement, width: number, height: number) => CanvasRenderingContext2D | null
  startDrawing: (ctx: CanvasRenderingContext2D, point: DrawingPoint, settings: ToolSettings) => void
  continueDrawing: (ctx: CanvasRenderingContext2D, point: DrawingPoint, settings: ToolSettings) => void
  endDrawing: (ctx: CanvasRenderingContext2D) => void
  eraseAt: (ctx: CanvasRenderingContext2D, point: DrawingPoint, size: number) => void
  clearCanvas: (ctx: CanvasRenderingContext2D, bounds: CanvasBounds) => void
  flipCanvas: (ctx: CanvasRenderingContext2D, bounds: CanvasBounds) => void
  getImageData: (ctx: CanvasRenderingContext2D, bounds: CanvasBounds) => ImageData
  setImageData: (ctx: CanvasRenderingContext2D, imageData: ImageData, x?: number, y?: number) => void
}

/**
 * Drawing history management interface
 */
export interface DrawingHistoryManager {
  addToHistory: (imageData: ImageData) => void
  undo: () => ImageData | null
  redo: () => ImageData | null
  canUndo: () => boolean
  canRedo: () => boolean
  clearHistory: () => void
  getHistoryLength: () => number
}

/**
 * Animation tool functions interface
 */
export interface AnimationToolFunctions {
  calculatePosition: (currentPos: { x: number, y: number }, velocity: { x: number, y: number }, deltaTime: number) => { x: number, y: number }
  checkBoundaries: (position: { x: number, y: number }, bounds: CanvasBounds, fishDimensions: { width: number, height: number }) => { position: { x: number, y: number }, velocity: { x: number, y: number } }
  updateTailAnimation: (currentPhase: number, speed: number) => number
  calculateTailOffset: (phase: number, amplitude: number) => number
}

/**
 * Fish animator interface
 */
export interface FishAnimatorInterface {
  startAnimation: () => void
  stopAnimation: () => void
  updatePosition: () => void
  updateTailAnimation: () => void
  checkBoundaries: () => void
  render: () => void
  setVelocity: (velocity: { x: number, y: number }) => void
  getPosition: () => { x: number, y: number }
  isAnimating: () => boolean
}

/**
 * Tool event handlers interface
 */
export interface ToolEventHandlers {
  onMouseDown: (event: MouseEvent) => void
  onMouseMove: (event: MouseEvent) => void
  onMouseUp: (event: MouseEvent) => void
  onTouchStart: (event: TouchEvent) => void
  onTouchMove: (event: TouchEvent) => void
  onTouchEnd: (event: TouchEvent) => void
}