// Fish animation controller for managing fish swimming behavior

import type { Position, Velocity, Dimensions, CanvasBounds, AnimationState, FishData } from '../types'
import {
  calculatePosition,
  checkBoundaries,
  updateTailAnimation,
  calculateTailOffset,
  createAnimationLoop,
  generateRandomVelocity,
  ANIMATION_CONFIG
} from './animationUtils'

/**
 * Fish animator class for controlling fish swimming animations
 */
export class FishAnimator {
  private fishData: FishData
  private bounds: CanvasBounds
  private animationLoop: ReturnType<typeof createAnimationLoop>
  private onUpdate?: (fishData: FishData) => void
  private lastUpdateTime: number = 0
  
  constructor(
    fishData: FishData,
    bounds: CanvasBounds,
    onUpdate?: (fishData: FishData) => void
  ) {
    this.fishData = { ...fishData }
    this.bounds = bounds
    this.onUpdate = onUpdate
    
    // Initialize animation loop
    this.animationLoop = createAnimationLoop((deltaTime) => {
      this.animate(deltaTime)
    })
    
    // Ensure fish has valid initial velocity
    if (!this.fishData.velocity || (this.fishData.velocity.x === 0 && this.fishData.velocity.y === 0)) {
      this.fishData.velocity = generateRandomVelocity()
    }
  }
  
  /**
   * Start the animation
   */
  startAnimation(): void {
    this.lastUpdateTime = performance.now()
    this.animationLoop.start()
  }
  
  /**
   * Stop the animation
   */
  stopAnimation(): void {
    this.animationLoop.stop()
  }
  
  /**
   * Check if animation is currently running
   */
  isAnimating(): boolean {
    return this.animationLoop.isRunning()
  }
  
  /**
   * Main animation loop
   */
  private animate(deltaTime: number): void {
    this.updatePosition(deltaTime)
    this.updateTailAnimation(deltaTime)
    this.checkBoundaries()
    this.render()
  }
  
  /**
   * Update fish position based on velocity
   */
  updatePosition(deltaTime?: number): void {
    const dt = deltaTime || ANIMATION_CONFIG.FRAME_TIME
    
    this.fishData.position = calculatePosition(
      this.fishData.position,
      this.fishData.velocity,
      dt
    )
  }
  
  /**
   * Update tail animation phase
   */
  updateTailAnimation(deltaTime?: number): void {
    const dt = deltaTime || ANIMATION_CONFIG.FRAME_TIME
    const speed = Math.abs(this.fishData.velocity.x) + Math.abs(this.fishData.velocity.y)
    const normalizedSpeed = Math.max(0.5, Math.min(2, speed / 2)) // Normalize speed to 0.5-2 range
    
    this.fishData.animationState.tailPhase = updateTailAnimation(
      this.fishData.animationState.tailPhase,
      normalizedSpeed
    )
    
    // Update direction based on velocity
    if (this.fishData.velocity.x !== 0) {
      this.fishData.animationState.direction = this.fishData.velocity.x > 0 ? 1 : -1
    }
  }
  
  /**
   * Check boundaries and handle collisions
   */
  checkBoundaries(): void {
    const result = checkBoundaries(
      this.fishData.position,
      this.fishData.velocity,
      this.bounds,
      this.fishData.dimensions
    )
    
    this.fishData.position = result.position
    this.fishData.velocity = result.velocity
    
    // Handle fish flipping when hitting boundaries
    if (result.shouldFlip) {
      this.fishData.animationState.direction = this.fishData.velocity.x > 0 ? 1 : -1
    }
  }
  
  /**
   * Render the fish (notify listeners of updates)
   */
  render(): void {
    if (this.onUpdate) {
      this.onUpdate({ ...this.fishData })
    }
  }
  
  /**
   * Set new velocity for the fish
   */
  setVelocity(velocity: Velocity): void {
    this.fishData.velocity = { ...velocity }
  }
  
  /**
   * Get current position
   */
  getPosition(): Position {
    return { ...this.fishData.position }
  }
  
  /**
   * Get current velocity
   */
  getVelocity(): Velocity {
    return { ...this.fishData.velocity }
  }
  
  /**
   * Get current animation state
   */
  getAnimationState(): AnimationState {
    return { ...this.fishData.animationState }
  }
  
  /**
   * Get current fish data
   */
  getFishData(): FishData {
    return { ...this.fishData }
  }
  
  /**
   * Update bounds (e.g., when window resizes)
   */
  updateBounds(newBounds: CanvasBounds): void {
    this.bounds = newBounds
    
    // Ensure fish is still within new bounds
    this.fishData.position.x = Math.max(
      ANIMATION_CONFIG.BOUNDARY_PADDING,
      Math.min(
        this.fishData.position.x,
        newBounds.width - this.fishData.dimensions.width - ANIMATION_CONFIG.BOUNDARY_PADDING
      )
    )
    
    this.fishData.position.y = Math.max(
      ANIMATION_CONFIG.BOUNDARY_PADDING,
      Math.min(
        this.fishData.position.y,
        newBounds.height - this.fishData.dimensions.height - ANIMATION_CONFIG.BOUNDARY_PADDING
      )
    )
  }
  
  /**
   * Set new position (useful for manual positioning)
   */
  setPosition(position: Position): void {
    this.fishData.position = { ...position }
  }
  
  /**
   * Get tail offset for current animation phase
   */
  getTailOffset(): number {
    return calculateTailOffset(this.fishData.animationState.tailPhase)
  }
  
  /**
   * Pause animation (keeps state but stops updates)
   */
  pause(): void {
    this.animationLoop.stop()
  }
  
  /**
   * Resume animation from current state
   */
  resume(): void {
    this.lastUpdateTime = performance.now()
    this.animationLoop.start()
  }
  
  /**
   * Reset fish to initial state
   */
  reset(initialData?: Partial<FishData>): void {
    if (initialData) {
      this.fishData = {
        ...this.fishData,
        ...initialData,
        position: initialData.position || this.fishData.position,
        velocity: initialData.velocity || generateRandomVelocity(),
        animationState: initialData.animationState || {
          tailPhase: 0,
          direction: 1
        }
      }
    } else {
      this.fishData.velocity = generateRandomVelocity()
      this.fishData.animationState = {
        tailPhase: 0,
        direction: 1
      }
    }
  }
  
  /**
   * Destroy the animator and clean up resources
   */
  destroy(): void {
    this.stopAnimation()
    this.onUpdate = undefined
  }
}

/**
 * Factory function to create a fish animator
 */
export function createFishAnimator(
  fishData: FishData,
  bounds: CanvasBounds,
  onUpdate?: (fishData: FishData) => void
): FishAnimator {
  return new FishAnimator(fishData, bounds, onUpdate)
}

/**
 * Utility function to create multiple fish animators
 */
export function createMultipleFishAnimators(
  fishDataArray: FishData[],
  bounds: CanvasBounds,
  onUpdate?: (fishData: FishData, index: number) => void
): FishAnimator[] {
  return fishDataArray.map((fishData, index) => {
    return new FishAnimator(fishData, bounds, (updatedFishData) => {
      if (onUpdate) {
        onUpdate(updatedFishData, index)
      }
    })
  })
}