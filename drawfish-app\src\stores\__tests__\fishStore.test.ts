import './setup'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useFishStore } from '../fishStore'

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
}
vi.stubGlobal('localStorage', localStorageMock)

describe('Fish Store', () => {
    beforeEach(() => {
        setActivePinia(createPinia())
        vi.clearAllMocks()
    })

    describe('初始状态', () => {
        it('应该有正确的初始状态', () => {
            const store = useFishStore()

            expect(store.currentFish).toBe(null)
            expect(store.fishCollection).toEqual([])
            expect(store.isSwimming).toBe(false)
            expect(store.swimBounds).toEqual({
                width: 800,
                height: 600
            })
        })

        it('应该有正确的计算属性', () => {
            const store = useFishStore()

            expect(store.hasFish).toBe(false)
            expect(store.fishCount).toBe(0)
            expect(store.getCurrentFishName).toBe(null)
        })
    })

    describe('鱼类创建', () => {
        it('应该能创建新鱼类', () => {
            const store = useFishStore()
            const mockImageData = new ImageData(100, 100)

            const fish = store.createFish(mockImageData, 'Test Fish')

            expect(fish.name).toBe('Test Fish')
            expect(fish.imageData).toBe(mockImageData)
            expect(fish.dimensions.width).toBe(100)
            expect(fish.dimensions.height).toBe(100)
            expect(fish.id).toMatch(/^fish_\d+_[a-z0-9]+$/)
            expect(fish.createdAt).toBeInstanceOf(Date)

            // 验证位置在边界内
            expect(fish.position.x).toBeGreaterThanOrEqual(50)
            expect(fish.position.x).toBeLessThanOrEqual(750)
            expect(fish.position.y).toBeGreaterThanOrEqual(50)
            expect(fish.position.y).toBeLessThanOrEqual(550)

            // 验证速度范围
            expect(fish.velocity.x).toBeGreaterThanOrEqual(-2)
            expect(fish.velocity.x).toBeLessThanOrEqual(2)
            expect(fish.velocity.y).toBeGreaterThanOrEqual(-1)
            expect(fish.velocity.y).toBeLessThanOrEqual(1)

            // 验证动画状态
            expect(fish.animationState.direction).toBeOneOf([-1, 1])
            expect(fish.animationState.tailPhase).toBeGreaterThanOrEqual(0)
            expect(fish.animationState.tailPhase).toBeLessThanOrEqual(Math.PI * 2)
        })

        it('应该处理空名称', () => {
            const store = useFishStore()
            const mockImageData = new ImageData(100, 100)

            const fish1 = store.createFish(mockImageData, '')
            expect(fish1.name).toBe('Unnamed Fish')

            const fish2 = store.createFish(mockImageData, '   ')
            expect(fish2.name).toBe('Unnamed Fish')
        })

        it('应该处理字符串类型的imageData', () => {
            const store = useFishStore()
            const base64Data = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=='

            const fish = store.createFish(base64Data, 'Base64 Fish')

            expect(fish.imageData).toBe(base64Data)
            expect(fish.dimensions.width).toBe(400) // 默认宽度
            expect(fish.dimensions.height).toBe(300) // 默认高度
        })

        it('创建鱼类后应该设置为当前鱼类并添加到集合', () => {
            const store = useFishStore()
            const mockImageData = new ImageData(100, 100)

            const fish = store.createFish(mockImageData, 'Test Fish')

            expect(store.currentFish).toStrictEqual(fish)
            expect(store.fishCollection).toContainEqual(fish)
            expect(store.hasFish).toBe(true)
            expect(store.fishCount).toBe(1)
            expect(store.getCurrentFishName).toBe('Test Fish')
        })
    })

    describe('鱼类管理', () => {
        it('应该能设置当前鱼类', () => {
            const store = useFishStore()
            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            store.setCurrentFish(mockFish)
            expect(store.currentFish).toStrictEqual(mockFish)

            store.setCurrentFish(null)
            expect(store.currentFish).toBe(null)
        })

        it('应该能添加鱼类到集合', () => {
            const store = useFishStore()
            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            store.addToCollection(mockFish)

            expect(store.fishCollection).toContainEqual(mockFish)
            expect(store.fishCount).toBe(1)
            expect(localStorageMock.setItem).toHaveBeenCalledWith(
                'drawfish_collection',
                expect.any(String)
            )
        })

        it('应该能更新已存在的鱼类', () => {
            const store = useFishStore()
            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            store.addToCollection(mockFish)

            const updatedFish = { ...mockFish, name: 'Updated Fish' }
            store.addToCollection(updatedFish)

            expect(store.fishCount).toBe(1) // 数量不变
            expect(store.fishCollection[0].name).toBe('Updated Fish')
        })

        it('应该能从集合中移除鱼类', () => {
            const store = useFishStore()
            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            store.addToCollection(mockFish)
            store.setCurrentFish(mockFish)

            store.removeFromCollection('test-fish')

            expect(store.fishCollection).not.toContain(mockFish)
            expect(store.currentFish).toBe(null) // 当前鱼类也被清除
            expect(store.fishCount).toBe(0)
        })

        it('应该能清空鱼类集合', () => {
            const store = useFishStore()
            store.createFish(new ImageData(100, 100), 'Fish 1')
            store.createFish(new ImageData(100, 100), 'Fish 2')

            expect(store.fishCount).toBe(2)

            store.clearCollection()

            expect(store.fishCollection).toEqual([])
            expect(store.currentFish).toBe(null)
            expect(store.fishCount).toBe(0)
        })
    })

    describe('游泳控制', () => {
        it('应该能开始和停止游泳', () => {
            const store = useFishStore()

            store.startSwimming()
            expect(store.isSwimming).toBe(true)

            store.stopSwimming()
            expect(store.isSwimming).toBe(false)
        })

        it('应该能设置游泳边界', () => {
            const store = useFishStore()

            store.setSwimBounds({ width: 1000, height: 800 })

            expect(store.swimBounds.width).toBe(1000)
            expect(store.swimBounds.height).toBe(800)
        })
    })

    describe('鱼类状态更新', () => {
        let store: ReturnType<typeof useFishStore>
        let mockFish: any

        beforeEach(() => {
            store = useFishStore()
            mockFish = store.createFish(new ImageData(100, 100), 'Test Fish')
        })

        it('应该能更新鱼类位置', () => {
            const newPosition = { x: 200, y: 300 }

            store.updateFishPosition(mockFish.id, newPosition)

            const updatedFish = store.fishCollection.find(f => f.id === mockFish.id)
            expect(updatedFish?.position).toEqual(newPosition)
            expect(store.currentFish?.position).toEqual(newPosition)
        })

        it('应该能更新鱼类速度', () => {
            const newVelocity = { x: 3, y: 2 }

            store.updateFishVelocity(mockFish.id, newVelocity)

            const updatedFish = store.fishCollection.find(f => f.id === mockFish.id)
            expect(updatedFish?.velocity).toEqual(newVelocity)
            expect(store.currentFish?.velocity).toEqual(newVelocity)
        })

        it('应该能更新鱼类动画状态', () => {
            const newAnimationState = { tailPhase: Math.PI, direction: -1 }

            store.updateAnimationState(mockFish.id, newAnimationState)

            const updatedFish = store.fishCollection.find(f => f.id === mockFish.id)
            expect(updatedFish?.animationState).toEqual(newAnimationState)
            expect(store.currentFish?.animationState).toEqual(newAnimationState)
        })

        it('应该能部分更新动画状态', () => {
            const originalAnimationState = { ...mockFish.animationState }

            store.updateAnimationState(mockFish.id, { tailPhase: Math.PI })

            const updatedFish = store.fishCollection.find(f => f.id === mockFish.id)
            expect(updatedFish?.animationState.tailPhase).toBe(Math.PI)
            expect(updatedFish?.animationState.direction).toBe(originalAnimationState.direction)
        })

        it('不存在的鱼类ID不应该引起错误', () => {
            expect(() => {
                store.updateFishPosition('non-existent', { x: 0, y: 0 })
                store.updateFishVelocity('non-existent', { x: 0, y: 0 })
                store.updateAnimationState('non-existent', { tailPhase: 0 })
            }).not.toThrow()
        })
    })

    describe('数据持久化', () => {
        it('应该在初始化时尝试加载数据', () => {
            // Create a new store instance to trigger initialization
            useFishStore()
            expect(localStorageMock.getItem).toHaveBeenCalledWith('drawfish_collection')
        })

        it('应该在添加鱼类时保存数据', () => {
            const store = useFishStore()
            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            store.addToCollection(mockFish)

            expect(localStorageMock.setItem).toHaveBeenCalledWith(
                'drawfish_collection',
                expect.stringContaining('test-fish')
            )
        })

        it('应该在移除鱼类时保存数据', () => {
            const store = useFishStore()
            const mockFish = store.createFish(new ImageData(100, 100), 'Test Fish')

            vi.clearAllMocks() // 清除创建时的调用

            store.removeFromCollection(mockFish.id)

            expect(localStorageMock.setItem).toHaveBeenCalledWith(
                'drawfish_collection',
                '[]'
            )
        })

        it('应该处理localStorage错误', () => {
            const store = useFishStore()
            const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { })

            localStorageMock.setItem.mockImplementation(() => {
                throw new Error('Storage quota exceeded')
            })

            const mockFish = {
                id: 'test-fish',
                name: 'Test Fish',
                imageData: new ImageData(100, 100),
                createdAt: new Date(),
                dimensions: { width: 100, height: 100 },
                position: { x: 100, y: 100 },
                velocity: { x: 1, y: 1 },
                animationState: { tailPhase: 0, direction: 1 }
            }

            expect(() => store.addToCollection(mockFish)).not.toThrow()
            expect(consoleSpy).toHaveBeenCalledWith('Failed to persist fish collection:', expect.any(Error))

            consoleSpy.mockRestore()
        })
    })
})