import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import ColorPicker from '../ColorPicker.vue'
import { useDrawingStore } from '@/stores/drawingStore'

describe('ColorPicker', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders correctly with default state', () => {
    const wrapper = mount(ColorPicker)
    
    // Check if component renders
    expect(wrapper.find('.color-picker').exists()).toBe(true)
    
    // Check if current color section exists
    expect(wrapper.find('.current-color-section').exists()).toBe(true)
    expect(wrapper.find('.current-color-display').exists()).toBe(true)
    
    // Check if preset colors are rendered
    expect(wrapper.find('.preset-colors').exists()).toBe(true)
    const colorButtons = wrapper.findAll('.color-button')
    expect(colorButtons.length).toBe(16) // Should have 16 preset colors
    
    // Check if custom color input exists
    expect(wrapper.find('.custom-color-input').exists()).toBe(true)
  })

  it('displays current color from store', () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    
    // Default color should be black
    const currentColorDisplay = wrapper.find('.current-color-display')
    expect(currentColorDisplay.attributes('style')).toContain('background-color: rgb(0, 0, 0)')
    
    // Change color in store
    store.setColor('#FF0000')
    
    // Wait for reactivity
    wrapper.vm.$nextTick(() => {
      expect(currentColorDisplay.attributes('style')).toContain('background-color: rgb(255, 0, 0)')
    })
  })

  it('highlights active color button', async () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    
    // Set color to red
    store.setColor('#FF0000')
    await wrapper.vm.$nextTick()
    
    // Find the red color button
    const colorButtons = wrapper.findAll('.color-button')
    const redButton = colorButtons.find(button => 
      button.attributes('style')?.includes('background-color: rgb(255, 0, 0)')
    )
    
    expect(redButton?.classes()).toContain('active')
  })

  it('calls setColor when preset color button is clicked', async () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    const setColorSpy = vi.spyOn(store, 'setColor')
    
    // Click on the red color button
    const colorButtons = wrapper.findAll('.color-button')
    const redButton = colorButtons.find(button => 
      button.attributes('style')?.includes('background-color: rgb(255, 0, 0)')
    )
    
    await redButton?.trigger('click')
    
    expect(setColorSpy).toHaveBeenCalledWith('#FF0000')
  })

  it('calls setColor when custom color input changes', async () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    const setColorSpy = vi.spyOn(store, 'setColor')
    
    const customColorInput = wrapper.find('.custom-color-input')
    
    // Simulate color input change
    await customColorInput.setValue('#123456')
    await customColorInput.trigger('input')
    
    expect(setColorSpy).toHaveBeenCalledWith('#123456')
  })

  it('has proper accessibility attributes', () => {
    const wrapper = mount(ColorPicker)
    
    // Check color buttons have aria-labels
    const colorButtons = wrapper.findAll('.color-button')
    colorButtons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
      expect(button.attributes('title')).toBeDefined()
    })
    
    // Check custom color input has proper label
    const customColorInput = wrapper.find('.custom-color-input')
    expect(customColorInput.attributes('id')).toBe('custom-color-input')
    expect(customColorInput.attributes('aria-label')).toBeDefined()
    
    const label = wrapper.find('label[for="custom-color-input"]')
    expect(label.exists()).toBe(true)
  })

  it('displays correct preset colors', () => {
    const wrapper = mount(ColorPicker)
    const colorButtons = wrapper.findAll('.color-button')
    
    const expectedColors = [
      '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00',
      '#FF00FF', '#00FFFF', '#FFA500', '#800080', '#FFC0CB', '#A52A2A',
      '#808080', '#90EE90', '#87CEEB', '#DDA0DD'
    ]
    
    expect(colorButtons.length).toBe(expectedColors.length)
    
    // Check if each expected color is present
    expectedColors.forEach(color => {
      const button = colorButtons.find(btn => 
        btn.attributes('title')?.includes(color)
      )
      expect(button).toBeDefined()
    })
  })

  it('updates custom color input value when store color changes', async () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    
    // Change color in store
    store.setColor('#ABCDEF')
    await wrapper.vm.$nextTick()
    
    const customColorInput = wrapper.find('.custom-color-input')
    expect(customColorInput.element.value.toUpperCase()).toBe('#ABCDEF')
  })

  it('handles invalid custom color input gracefully', async () => {
    const wrapper = mount(ColorPicker)
    const store = useDrawingStore()
    const setColorSpy = vi.spyOn(store, 'setColor')
    
    const customColorInput = wrapper.find('.custom-color-input')
    
    // Simulate empty input
    await customColorInput.setValue('')
    await customColorInput.trigger('input')
    
    // setColor should not be called with empty value
    expect(setColorSpy).not.toHaveBeenCalledWith('')
  })
})