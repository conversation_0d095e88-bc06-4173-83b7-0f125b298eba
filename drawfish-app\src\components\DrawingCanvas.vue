<template>
  <div class="drawing-canvas-container" ref="containerRef">
    <canvas
      ref="canvasRef"
      class="drawing-canvas"
      :data-tool="currentTool"
      :width="canvasWidth"
      :height="canvasHeight"
      @mousedown="handleMouseDown"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mouseleave="handleMouseUp"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchEnd"
    />
    
    <!-- Drawing preview overlay for real-time feedback -->
    <canvas
      v-if="showPreview && isDrawing"
      ref="previewCanvasRef"
      class="preview-canvas"
      :width="canvasWidth"
      :height="canvasHeight"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useDrawingStore } from '@/stores/drawingStore'
import { CanvasUtils, DrawingHistoryManager, DrawingPathRecorder } from '@/utils/canvasUtils'
import type { DrawingPoint, CanvasBounds } from '@/types'

// Props
interface Props {
  width?: number
  height?: number
  showPreview?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600,
  showPreview: true
})

// Emits
const emit = defineEmits<{
  canvasReady: [canvas: HTMLCanvasElement, context: CanvasRenderingContext2D]
  drawingStart: [point: DrawingPoint]
  drawingMove: [point: DrawingPoint]
  drawingEnd: [point: DrawingPoint]
  canvasCleared: []
}>()

// Store
const drawingStore = useDrawingStore()

// Refs
const containerRef = ref<HTMLDivElement>()
const canvasRef = ref<HTMLCanvasElement>()
const previewCanvasRef = ref<HTMLCanvasElement>()

// State
const canvasContext = ref<CanvasRenderingContext2D | null>(null)
const previewContext = ref<CanvasRenderingContext2D | null>(null)
const historyManager = ref<DrawingHistoryManager>(new DrawingHistoryManager())
const pathRecorder = ref<DrawingPathRecorder>(new DrawingPathRecorder())
const lastPoint = ref<DrawingPoint | null>(null)

// Computed
const canvasWidth = computed(() => props.width)
const canvasHeight = computed(() => props.height)
const isDrawing = computed(() => drawingStore.isDrawing)
const currentTool = computed(() => drawingStore.currentTool)
const toolSettings = computed(() => drawingStore.currentToolSettings)

// Canvas bounds
const canvasBounds = computed((): CanvasBounds => {
  if (!canvasRef.value) {
    return { width: props.width, height: props.height, offsetX: 0, offsetY: 0 }
  }
  return CanvasUtils.getCanvasBounds(canvasRef.value)
})

// Initialize canvas
const initializeCanvas = async () => {
  await nextTick()
  
  if (!canvasRef.value) {
    console.error('Canvas ref not available')
    return
  }

  // Initialize main canvas
  const ctx = CanvasUtils.initializeCanvas(canvasRef.value, canvasWidth.value, canvasHeight.value)
  if (!ctx) {
    console.error('Failed to initialize canvas context')
    return
  }
  
  canvasContext.value = ctx
  
  // Initialize preview canvas if enabled
  if (props.showPreview && previewCanvasRef.value) {
    const previewCtx = CanvasUtils.initializeCanvas(
      previewCanvasRef.value, 
      canvasWidth.value, 
      canvasHeight.value
    )
    previewContext.value = previewCtx
  }

  // Save initial state to history
  const initialImageData = CanvasUtils.getImageData(ctx, canvasBounds.value)
  historyManager.value.addToHistory(initialImageData)

  emit('canvasReady', canvasRef.value, ctx)
}

// Convert screen coordinates to canvas coordinates
const getCanvasPoint = (clientX: number, clientY: number, pressure: number = 1): DrawingPoint => {
  if (!canvasRef.value) {
    return { x: 0, y: 0, pressure }
  }
  
  const coords = CanvasUtils.screenToCanvasCoordinates(clientX, clientY, canvasRef.value)
  return {
    x: coords.x,
    y: coords.y,
    pressure
  }
}

// Start drawing
const startDrawing = (point: DrawingPoint) => {
  if (!canvasContext.value) return

  drawingStore.setDrawingState(true)
  lastPoint.value = point
  
  // Start path recording
  pathRecorder.value.startPath(point)
  
  // Start stroke recording in store
  drawingStore.startStroke(point)
  
  // Start drawing on canvas
  CanvasUtils.startDrawing(canvasContext.value, point, toolSettings.value)
  
  emit('drawingStart', point)
}

// Continue drawing
const continueDrawing = (point: DrawingPoint) => {
  if (!canvasContext.value || !isDrawing.value || !lastPoint.value) return

  // Add point to path
  pathRecorder.value.addPoint(point)
  
  // Add point to stroke in store
  drawingStore.addPointToStroke(point)
  
  // Draw on canvas
  CanvasUtils.continueDrawing(canvasContext.value, point, toolSettings.value)
  
  // Update preview if enabled
  if (props.showPreview && previewContext.value) {
    updatePreview(point)
  }
  
  lastPoint.value = point
  emit('drawingMove', point)
}

// End drawing
const endDrawing = (point: DrawingPoint) => {
  if (!canvasContext.value || !isDrawing.value) return

  // End drawing on canvas
  CanvasUtils.endDrawing(canvasContext.value)
  
  // End path recording
  pathRecorder.value.endPath()
  
  // Finish stroke recording in store
  drawingStore.finishStroke()
  
  // Save to history
  const imageData = CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
  historyManager.value.addToHistory(imageData)
  
  // Clear preview
  if (previewContext.value) {
    CanvasUtils.clearCanvas(previewContext.value, canvasBounds.value)
  }
  
  drawingStore.setDrawingState(false)
  lastPoint.value = null
  
  emit('drawingEnd', point)
}

// Update preview canvas
const updatePreview = (point: DrawingPoint) => {
  if (!previewContext.value || !lastPoint.value) return

  // Clear previous preview
  CanvasUtils.clearCanvas(previewContext.value, canvasBounds.value)
  
  // Draw preview line
  CanvasUtils.startDrawing(previewContext.value, lastPoint.value, toolSettings.value)
  CanvasUtils.continueDrawing(previewContext.value, point, toolSettings.value)
}

// Handle eraser tool
const handleErase = (point: DrawingPoint) => {
  if (!canvasContext.value) return
  
  if (lastPoint.value && isDrawing.value) {
    // Use smooth erasing along path for better user experience
    CanvasUtils.eraseAlongPath(canvasContext.value, lastPoint.value, point, toolSettings.value.size)
  } else {
    // Use point erasing for initial touch/click
    CanvasUtils.eraseAt(canvasContext.value, point, toolSettings.value.size)
  }
}

// Mouse event handlers
const handleMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  const point = getCanvasPoint(event.clientX, event.clientY)
  
  if (currentTool.value === 'eraser') {
    drawingStore.setDrawingState(true)
    handleErase(point)
    lastPoint.value = point
  } else {
    startDrawing(point)
  }
}

const handleMouseMove = (event: MouseEvent) => {
  event.preventDefault()
  const point = getCanvasPoint(event.clientX, event.clientY)
  
  if (currentTool.value === 'eraser' && isDrawing.value) {
    handleErase(point)
    lastPoint.value = point
  } else if (isDrawing.value) {
    continueDrawing(point)
  }
}

const handleMouseUp = (event: MouseEvent) => {
  if (!isDrawing.value) return
  
  event.preventDefault()
  const point = getCanvasPoint(event.clientX, event.clientY)
  
  if (currentTool.value === 'eraser') {
    // Save eraser state to history
    if (canvasContext.value) {
      const imageData = CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
      historyManager.value.addToHistory(imageData)
    }
    drawingStore.setDrawingState(false)
    lastPoint.value = null
  } else {
    endDrawing(point)
  }
}

// Touch event handlers
const handleTouchStart = (event: TouchEvent) => {
  event.preventDefault()
  if (event.touches.length !== 1) return
  
  const touch = event.touches[0]
  const point = getCanvasPoint(touch.clientX, touch.clientY, touch.force || 1)
  
  if (currentTool.value === 'eraser') {
    drawingStore.setDrawingState(true)
    handleErase(point)
    lastPoint.value = point
  } else {
    startDrawing(point)
  }
}

const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault()
  if (event.touches.length !== 1 || !isDrawing.value) return
  
  const touch = event.touches[0]
  const point = getCanvasPoint(touch.clientX, touch.clientY, touch.force || 1)
  
  if (currentTool.value === 'eraser') {
    handleErase(point)
    lastPoint.value = point
  } else {
    continueDrawing(point)
  }
}

const handleTouchEnd = (event: TouchEvent) => {
  if (!isDrawing.value) return
  
  event.preventDefault()
  
  // Get the last touch point if available
  const touch = event.changedTouches[0]
  const point = touch ? getCanvasPoint(touch.clientX, touch.clientY, touch.force || 1) : lastPoint.value
  
  if (currentTool.value === 'eraser') {
    // Save eraser state to history
    if (canvasContext.value) {
      const imageData = CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
      historyManager.value.addToHistory(imageData)
    }
    drawingStore.setDrawingState(false)
    lastPoint.value = null
  } else if (point) {
    endDrawing(point)
  }
}

// Public methods for external control
const undo = () => {
  if (!canvasContext.value) return false
  
  const previousState = historyManager.value.undo()
  if (previousState) {
    CanvasUtils.setImageData(canvasContext.value, previousState)
    return true
  }
  return false
}

const redo = () => {
  if (!canvasContext.value) return false
  
  const nextState = historyManager.value.redo()
  if (nextState) {
    CanvasUtils.setImageData(canvasContext.value, nextState)
    return true
  }
  return false
}

const clearCanvas = () => {
  if (!canvasContext.value) return
  
  CanvasUtils.clearCanvas(canvasContext.value, canvasBounds.value)
  
  // Save cleared state to history
  const imageData = CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
  historyManager.value.addToHistory(imageData)
  
  emit('canvasCleared')
}

const flipCanvas = () => {
  if (!canvasContext.value) return
  
  CanvasUtils.flipCanvas(canvasContext.value, canvasBounds.value)
  
  // Save flipped state to history
  const imageData = CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
  historyManager.value.addToHistory(imageData)
}

const getCanvasImageData = (): ImageData | null => {
  if (!canvasContext.value) return null
  return CanvasUtils.getImageData(canvasContext.value, canvasBounds.value)
}

const setCanvasImageData = (imageData: ImageData) => {
  if (!canvasContext.value) return
  CanvasUtils.setImageData(canvasContext.value, imageData)
}

// Watch for canvas size changes
watch([canvasWidth, canvasHeight], () => {
  initializeCanvas()
})

// Lifecycle
onMounted(() => {
  initializeCanvas()
})

onUnmounted(() => {
  // Clean up any ongoing drawing
  if (isDrawing.value) {
    drawingStore.setDrawingState(false)
  }
})

// Expose public methods
defineExpose({
  undo,
  redo,
  clearCanvas,
  flipCanvas,
  getCanvasImageData,
  setCanvasImageData,
  canUndo: () => historyManager.value.canUndo(),
  canRedo: () => historyManager.value.canRedo()
})
</script>

<style scoped>
.drawing-canvas-container {
  position: relative;
  display: inline-block;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.drawing-canvas {
  display: block;
  cursor: crosshair;
  touch-action: none;
  user-select: none;
}

.drawing-canvas:hover {
  cursor: crosshair;
}

.preview-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  opacity: 0.7;
}

/* Tool-specific cursors */
.drawing-canvas[data-tool="brush"] {
  cursor: crosshair;
}

.drawing-canvas[data-tool="eraser"] {
  cursor: grab;
}

.drawing-canvas[data-tool="eraser"]:active {
  cursor: grabbing;
}

/* Responsive design */
@media (max-width: 768px) {
  .drawing-canvas-container {
    border-width: 1px;
  }
}
</style>