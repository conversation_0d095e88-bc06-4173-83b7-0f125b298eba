import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import FishNamingDialog from '../FishNamingDialog.vue'

// Mock ImageData
global.ImageData = class ImageData {
  width: number
  height: number
  data: Uint8ClampedArray
  colorSpace: PredefinedColorSpace = 'srgb'

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
} as any

// Mock canvas context
const mockCanvasContext = {
  clearRect: vi.fn(),
  fillRect: vi.fn(),
  strokeRect: vi.fn(),
  drawImage: vi.fn(),
  putImageData: vi.fn(),
  getContext: vi.fn(),
  fillStyle: '',
  strokeStyle: '',
  lineWidth: 0,
  font: '',
  textAlign: '',
  fillText: vi.fn()
}

// Mock HTMLCanvasElement
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: vi.fn(() => mockCanvasContext)
})

// Mock document.createElement for canvas
const originalCreateElement = document.createElement
document.createElement = vi.fn((tagName) => {
  if (tagName === 'canvas') {
    const canvas = originalCreateElement.call(document, tagName) as HTMLCanvasElement
    canvas.getContext = vi.fn(() => mockCanvasContext)
    return canvas
  }
  return originalCreateElement.call(document, tagName)
})

describe('FishNamingDialog', () => {
  const mockImageData = new ImageData(100, 100)
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly with fish image', () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    expect(wrapper.find('.dialog-title').text()).toBe('🐠 给你的鱼起个名字')
    expect(wrapper.find('.name-input').exists()).toBe(true)
    expect(wrapper.find('.confirm-button').exists()).toBe(true)
    expect(wrapper.find('.cancel-button').exists()).toBe(true)
  })

  it('renders without fish image', () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: null
      }
    })

    expect(wrapper.find('.dialog-container').exists()).toBe(true)
    expect(wrapper.find('.fish-preview-canvas').exists()).toBe(true)
  })

  it('updates fish name when input changes', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('My Beautiful Fish')

    expect((input.element as HTMLInputElement).value).toBe('My Beautiful Fish')
  })

  it('shows character count', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('Test Fish')

    expect(wrapper.find('.char-count').text()).toBe('9/50')
  })

  it('shows warning when approaching character limit', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('A'.repeat(45)) // 45 characters

    const charCount = wrapper.find('.char-count')
    expect(charCount.text()).toBe('45/50')
    expect(charCount.classes()).toContain('warning')
  })

  it('disables confirm button when name is empty', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const confirmButton = wrapper.find('.confirm-button')
    expect(confirmButton.classes()).toContain('disabled')
    expect((confirmButton.element as HTMLButtonElement).disabled).toBe(true)
  })

  it('enables confirm button when name is valid', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('Valid Fish Name')

    const confirmButton = wrapper.find('.confirm-button')
    expect(confirmButton.classes()).not.toContain('disabled')
    expect((confirmButton.element as HTMLButtonElement).disabled).toBe(false)
  })

  it('shows error for invalid characters', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('Fish<Name>')

    const confirmButton = wrapper.find('.confirm-button')
    await confirmButton.trigger('click')

    expect(wrapper.find('.error-message').exists()).toBe(true)
    expect(wrapper.find('.name-input').classes()).toContain('error')
  })

  it('shows error for name too long', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('A'.repeat(51)) // 51 characters

    const confirmButton = wrapper.find('.confirm-button')
    await confirmButton.trigger('click')

    const errorMessage = wrapper.find('.error-message')
    if (errorMessage.exists()) {
      expect(errorMessage.text()).toBe('名字太长了，最多50个字符')
    }
  })

  it('emits confirm event with trimmed name', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('  My Fish  ')

    const confirmButton = wrapper.find('.confirm-button')
    await confirmButton.trigger('click')

    expect(wrapper.emitted('confirm')).toBeTruthy()
    expect(wrapper.emitted('confirm')![0]).toEqual(['My Fish'])
  })

  it('emits cancel event when cancel button clicked', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const cancelButton = wrapper.find('.cancel-button')
    await cancelButton.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('emits cancel event when close button clicked', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const closeButton = wrapper.find('.close-button')
    await closeButton.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('emits cancel event when overlay clicked', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const overlay = wrapper.find('.dialog-overlay')
    await overlay.trigger('click')

    expect(wrapper.emitted('cancel')).toBeTruthy()
  })

  it('does not emit cancel when dialog container clicked', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const container = wrapper.find('.dialog-container')
    await container.trigger('click')

    expect(wrapper.emitted('cancel')).toBeFalsy()
  })

  it('submits form when Enter key pressed in input', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('Test Fish')
    await input.trigger('keyup.enter')

    expect(wrapper.emitted('confirm')).toBeTruthy()
    expect(wrapper.emitted('confirm')![0]).toEqual(['Test Fish'])
  })

  it('clears error when input changes', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    // First trigger an error
    const input = wrapper.find('.name-input')
    await input.setValue('Fish<>')
    
    const confirmButton = wrapper.find('.confirm-button')
    await confirmButton.trigger('click')

    expect(wrapper.find('.error-message').exists()).toBe(true)

    // Then change input to clear error
    await input.setValue('Valid Name')
    await input.trigger('input')

    expect(wrapper.find('.error-message').exists()).toBe(false)
    expect(wrapper.find('.name-input').classes()).not.toContain('error')
  })

  it('validates empty name after trimming', async () => {
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    const input = wrapper.find('.name-input')
    await input.setValue('   ')

    const confirmButton = wrapper.find('.confirm-button')
    await confirmButton.trigger('click')

    const errorMessage = wrapper.find('.error-message')
    if (errorMessage.exists()) {
      expect(errorMessage.text()).toBe('请输入鱼的名字')
    }
  })

  it('handles canvas drawing errors gracefully', () => {
    // This test verifies that the component renders even when canvas operations fail
    const wrapper = mount(FishNamingDialog, {
      props: {
        fishImage: mockImageData
      }
    })

    // Should not throw error and component should still render
    expect(wrapper.find('.fish-preview-canvas').exists()).toBe(true)
    expect(wrapper.find('.dialog-container').exists()).toBe(true)
  })
})