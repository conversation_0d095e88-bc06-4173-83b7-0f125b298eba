# 设计文档

## 概述

DrawFish应用是一个基于Vue3+Vite的单页面应用，使用HTML5 Canvas实现绘画功能，通过CSS动画和JavaScript实现鱼类游泳效果。应用采用组件化架构，使用Pinia进行状态管理，Vue Router处理页面路由。

## 架构

### 技术栈
- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **绘画技术**: HTML5 Canvas API
- **动画技术**: CSS Animations + Web Animations API
- **样式**: CSS3 + Flexbox/Grid

### 项目结构
```
src/
├── components/
│   ├── DrawingCanvas.vue      # 绘画画布组件
│   ├── ToolBar.vue           # 工具栏组件
│   ├── ColorPicker.vue       # 颜色选择器
│   ├── SizeSlider.vue        # 线条粗细滑块
│   ├── FishNamingDialog.vue  # 鱼类命名对话框
│   ├── SwimmingFish.vue      # 游泳的鱼组件
│   └── FishTank.vue          # 鱼缸背景组件
├── views/
│   ├── DrawingPage.vue       # 绘画页面
│   └── SwimmingPage.vue      # 游泳页面
├── stores/
│   ├── drawingStore.js       # 绘画状态管理
│   └── fishStore.js          # 鱼类数据管理
├── utils/
│   ├── canvasUtils.js        # Canvas工具函数
│   ├── animationUtils.js     # 动画工具函数
│   └── fishAnimator.js       # 鱼类动画控制器
├── router/
│   └── index.js              # 路由配置
└── api/
    └── fishApi.js            # API接口（如需要）
```

## 组件和接口

### 核心组件设计

#### 1. DrawingCanvas.vue
**职责**: 处理所有绘画交互和Canvas操作
**主要功能**:
- Canvas元素的初始化和配置
- 鼠标/触摸事件处理
- 绘画路径记录和渲染
- 撤销/重做功能实现
- 画布内容的导出

**接口**:
```typescript
interface DrawingCanvasProps {
  width: number
  height: number
  currentTool: DrawingTool
  currentColor: string
  brushSize: number
}

interface DrawingCanvasEmits {
  'canvas-updated': (imageData: ImageData) => void
  'drawing-complete': (fishData: FishData) => void
}
```

#### 2. ToolBar.vue
**职责**: 提供所有绘画工具的用户界面
**主要功能**:
- 颜色选择器集成
- 工具切换（画笔/擦除器）
- 线条粗细控制
- 操作按钮（撤销/清除/翻转）

#### 3. SwimmingFish.vue
**职责**: 渲染和动画化用户绘制的鱼
**主要功能**:
- 鱼类图像的渲染
- 尾巴摆动动画
- 游泳路径计算
- 边界碰撞检测

### 状态管理设计

#### DrawingStore (Pinia)
```javascript
export const useDrawingStore = defineStore('drawing', {
  state: () => ({
    currentTool: 'brush',
    currentColor: '#000000',
    brushSize: 5,
    canvasHistory: [],
    isDrawing: false,
    lastDrawnFish: null
  }),
  
  actions: {
    setTool(tool) { /* ... */ },
    setColor(color) { /* ... */ },
    setBrushSize(size) { /* ... */ },
    addToHistory(imageData) { /* ... */ },
    undo() { /* ... */ },
    clearCanvas() { /* ... */ },
    flipCanvas() { /* ... */ }
  }
})
```

#### FishStore (Pinia)
```javascript
export const useFishStore = defineStore('fish', {
  state: () => ({
    currentFish: null,
    fishCollection: [],
    isSwimming: false
  }),
  
  actions: {
    createFish(imageData, name) { /* ... */ },
    startSwimming() { /* ... */ },
    stopSwimming() { /* ... */ },
    saveFish(fish) { /* ... */ }
  }
})
```

## 数据模型

### FishData Interface
```typescript
interface FishData {
  id: string
  name: string
  imageData: ImageData | string  // Canvas ImageData或base64
  createdAt: Date
  dimensions: {
    width: number
    height: number
  }
  position: {
    x: number
    y: number
  }
  velocity: {
    x: number
    y: number
  }
  animationState: {
    tailPhase: number
    direction: number  // -1 for left, 1 for right
  }
}
```

### DrawingTool Type
```typescript
type DrawingTool = 'brush' | 'eraser'

interface ToolSettings {
  color: string
  size: number
  opacity: number
}
```

## 错误处理

### Canvas错误处理
- **Canvas不支持**: 检测浏览器Canvas支持，提供降级方案
- **内存溢出**: 限制画布尺寸和历史记录数量
- **绘画数据丢失**: 实现自动保存到localStorage

### 动画错误处理
- **性能问题**: 使用requestAnimationFrame优化动画
- **内存泄漏**: 正确清理动画定时器和事件监听器
- **浏览器兼容性**: 提供CSS动画降级方案

### 路由错误处理
- **无效路由**: 重定向到首页
- **状态丢失**: 路由守卫检查必要数据
- **浏览器后退**: 处理用户导航行为

## 测试策略

### 单元测试
- **Canvas工具函数**: 测试绘画逻辑和数据处理
- **Pinia Store**: 测试状态变更和动作
- **动画工具**: 测试动画计算函数
- **组件逻辑**: 测试Vue组件的方法和计算属性

### 集成测试
- **绘画流程**: 从工具选择到绘画完成的完整流程
- **页面导航**: 测试路由跳转和状态传递
- **动画系统**: 测试鱼类动画的完整生命周期

### 端到端测试
- **用户绘画流程**: 模拟完整的用户绘画体验
- **跨页面状态**: 测试数据在页面间的正确传递
- **响应式行为**: 测试不同屏幕尺寸下的功能

### 性能测试
- **Canvas渲染性能**: 测试大量绘画操作的性能
- **动画流畅度**: 确保60fps的动画表现
- **内存使用**: 监控长时间使用的内存消耗

## 实现细节

### Canvas绘画实现
```javascript
// 绘画路径记录
const drawingPath = {
  startDrawing(x, y) {
    this.isDrawing = true
    this.currentPath = [{ x, y, pressure: 1 }]
  },
  
  continueDrawing(x, y, pressure = 1) {
    if (!this.isDrawing) return
    this.currentPath.push({ x, y, pressure })
    this.renderPath()
  },
  
  endDrawing() {
    this.isDrawing = false
    this.saveToHistory()
  }
}
```

### 鱼类动画实现
```javascript
// 鱼类游泳动画
class FishAnimator {
  constructor(fishElement, bounds) {
    this.fish = fishElement
    this.bounds = bounds
    this.position = { x: 0, y: 0 }
    this.velocity = { x: 2, y: 1 }
    this.tailPhase = 0
  }
  
  animate() {
    this.updatePosition()
    this.updateTailAnimation()
    this.checkBoundaries()
    this.render()
    requestAnimationFrame(() => this.animate())
  }
  
  updateTailAnimation() {
    this.tailPhase += 0.2
    const tailOffset = Math.sin(this.tailPhase) * 10
    this.fish.style.transform = `translateX(${this.position.x}px) translateY(${this.position.y}px) skewX(${tailOffset}deg)`
  }
}
```

### 路由配置
```javascript
const routes = [
  {
    path: '/',
    name: 'Drawing',
    component: DrawingPage,
    meta: { title: 'Draw a Fish!' }
  },
  {
    path: '/swimming/:fishId?',
    name: 'Swimming',
    component: SwimmingPage,
    meta: { title: 'Watch Your Fish Swim!' },
    beforeEnter: (to, from, next) => {
      // 检查是否有鱼类数据
      const fishStore = useFishStore()
      if (!fishStore.currentFish) {
        next('/')
      } else {
        next()
      }
    }
  }
]
```