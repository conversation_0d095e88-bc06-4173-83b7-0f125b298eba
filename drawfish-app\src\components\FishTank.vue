<template>
  <div class="fish-tank" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 水族箱玻璃边框 -->
    <div class="tank-frame">
      <div class="glass-top"></div>
      <div class="glass-left"></div>
      <div class="glass-right"></div>
      <div class="glass-bottom"></div>
    </div>
    
    <!-- 水的背景 -->
    <div class="water-background">
      <!-- 水波纹效果 -->
      <div class="water-ripples">
        <div class="ripple ripple-1"></div>
        <div class="ripple ripple-2"></div>
        <div class="ripple ripple-3"></div>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="decorations">
      <!-- 海草 -->
      <div class="seaweed seaweed-left">
        <div class="seaweed-strand strand-1"></div>
        <div class="seaweed-strand strand-2"></div>
        <div class="seaweed-strand strand-3"></div>
      </div>
      
      <div class="seaweed seaweed-right">
        <div class="seaweed-strand strand-1"></div>
        <div class="seaweed-strand strand-2"></div>
      </div>
      
      <!-- 气泡 -->
      <div class="bubbles">
        <div class="bubble bubble-1"></div>
        <div class="bubble bubble-2"></div>
        <div class="bubble bubble-3"></div>
        <div class="bubble bubble-4"></div>
        <div class="bubble bubble-5"></div>
      </div>
      
      <!-- 沙子底部 -->
      <div class="sand-bottom"></div>
    </div>
    
    <!-- 内容插槽 -->
    <div class="tank-content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  width?: number
  height?: number
}

withDefaults(defineProps<Props>(), {
  width: 800,
  height: 600
})
</script>

<style scoped>
.fish-tank {
  position: relative;
  background: linear-gradient(
    to bottom,
    #87CEEB 0%,
    #4682B4 30%,
    #1E90FF 60%,
    #0066CC 100%
  );
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 
    0 0 20px rgba(0, 100, 200, 0.3),
    inset 0 0 50px rgba(255, 255, 255, 0.1);
}

/* 玻璃边框效果 */
.tank-frame {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.glass-top, .glass-bottom {
  position: absolute;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.4),
    rgba(255, 255, 255, 0.1)
  );
  border-radius: 10px 10px 0 0;
}

.glass-top {
  top: 0;
}

.glass-bottom {
  bottom: 0;
  border-radius: 0 0 10px 10px;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.4),
    rgba(255, 255, 255, 0.1)
  );
}

.glass-left, .glass-right {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 8px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.4),
    rgba(255, 255, 255, 0.1)
  );
}

.glass-left {
  left: 0;
}

.glass-right {
  right: 0;
  background: linear-gradient(
    to left,
    rgba(255, 255, 255, 0.4),
    rgba(255, 255, 255, 0.1)
  );
}

/* 水波纹效果 */
.water-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.water-ripples {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
}

.ripple {
  position: absolute;
  width: 100%;
  height: 20px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  animation: ripple-move 8s ease-in-out infinite;
}

.ripple-1 {
  top: 20%;
  animation-delay: 0s;
}

.ripple-2 {
  top: 50%;
  animation-delay: 2s;
}

.ripple-3 {
  top: 80%;
  animation-delay: 4s;
}

@keyframes ripple-move {
  0%, 100% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    transform: translateX(100%);
    opacity: 1;
  }
}

/* 装饰元素 */
.decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* 海草 */
.seaweed {
  position: absolute;
  bottom: 60px;
}

.seaweed-left {
  left: 20px;
}

.seaweed-right {
  right: 30px;
}

.seaweed-strand {
  width: 8px;
  background: linear-gradient(
    to top,
    #228B22 0%,
    #32CD32 50%,
    #90EE90 100%
  );
  border-radius: 4px;
  transform-origin: bottom center;
  animation: seaweed-sway 4s ease-in-out infinite;
  margin: 0 2px;
  display: inline-block;
}

.strand-1 {
  height: 80px;
  animation-delay: 0s;
}

.strand-2 {
  height: 100px;
  animation-delay: 1s;
}

.strand-3 {
  height: 60px;
  animation-delay: 2s;
}

@keyframes seaweed-sway {
  0%, 100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

/* 气泡 */
.bubbles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.bubble {
  position: absolute;
  background: radial-gradient(
    circle at 30% 30%,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.3)
  );
  border-radius: 50%;
  animation: bubble-rise 6s linear infinite;
}

.bubble-1 {
  width: 12px;
  height: 12px;
  left: 15%;
  animation-delay: 0s;
}

.bubble-2 {
  width: 8px;
  height: 8px;
  left: 25%;
  animation-delay: 1.5s;
}

.bubble-3 {
  width: 15px;
  height: 15px;
  left: 70%;
  animation-delay: 3s;
}

.bubble-4 {
  width: 6px;
  height: 6px;
  left: 80%;
  animation-delay: 4.5s;
}

.bubble-5 {
  width: 10px;
  height: 10px;
  left: 90%;
  animation-delay: 2s;
}

@keyframes bubble-rise {
  0% {
    bottom: -20px;
    opacity: 0;
    transform: translateX(0) scale(0.5);
  }
  10% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateX(20px) scale(1);
  }
  100% {
    bottom: 100%;
    opacity: 0;
    transform: translateX(40px) scale(0.5);
  }
}

/* 沙子底部 */
.sand-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(
    to top,
    #F4A460 0%,
    #DEB887 50%,
    rgba(222, 184, 135, 0.8) 100%
  );
  border-radius: 0 0 10px 10px;
}

.sand-bottom::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: repeating-linear-gradient(
    90deg,
    transparent 0px,
    rgba(255, 255, 255, 0.1) 2px,
    transparent 4px
  );
}

/* 内容区域 */
.tank-content {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fish-tank {
    border-radius: 8px;
  }
  
  .seaweed-strand {
    width: 6px;
  }
  
  .strand-1 {
    height: 60px;
  }
  
  .strand-2 {
    height: 80px;
  }
  
  .strand-3 {
    height: 45px;
  }
  
  .sand-bottom {
    height: 40px;
  }
  
  .bubble {
    transform: scale(0.8);
  }
}

@media (max-width: 480px) {
  .fish-tank {
    border-radius: 6px;
  }
  
  .glass-top, .glass-bottom,
  .glass-left, .glass-right {
    width: 6px;
    height: 6px;
  }
  
  .seaweed-strand {
    width: 4px;
  }
  
  .strand-1 {
    height: 40px;
  }
  
  .strand-2 {
    height: 60px;
  }
  
  .strand-3 {
    height: 30px;
  }
  
  .sand-bottom {
    height: 30px;
  }
  
  .bubble {
    transform: scale(0.6);
  }
}
</style>