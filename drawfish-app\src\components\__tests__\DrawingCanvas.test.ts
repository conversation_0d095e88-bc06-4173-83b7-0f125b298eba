import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest'
import { mount, type VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import DrawingCanvas from '../DrawingCanvas.vue'
import { useDrawingStore } from '@/stores/drawingStore'
import { CanvasUtils } from '@/utils/canvasUtils'

// Mock CanvasUtils
vi.mock('@/utils/canvasUtils', () => ({
    CanvasUtils: {
        initializeCanvas: vi.fn(),
        startDrawing: vi.fn(),
        continueDrawing: vi.fn(),
        endDrawing: vi.fn(),
        eraseAt: vi.fn(),
        clearCanvas: vi.fn(),
        flipCanvas: vi.fn(),
        getImageData: vi.fn(),
        setImageData: vi.fn(),
        getCanvasBounds: vi.fn(),
        screenToCanvasCoordinates: vi.fn()
    },
    DrawingHistoryManager: vi.fn().mockImplementation(() => ({
        addToHistory: vi.fn(),
        undo: vi.fn(),
        redo: vi.fn(),
        canUndo: vi.fn(() => false),
        canRedo: vi.fn(() => false),
        clearHistory: vi.fn()
    })),
    DrawingPathRecorder: vi.fn().mockImplementation(() => ({
        startPath: vi.fn(),
        addPoint: vi.fn(),
        endPath: vi.fn(() => []),
        getCurrentPath: vi.fn(() => []),
        isCurrentlyRecording: vi.fn(() => false),
        clearCurrentPath: vi.fn()
    }))
}))

// Mock ImageData for testing environment
class MockImageData {
    width: number
    height: number
    data: Uint8ClampedArray

    constructor(width: number, height: number) {
        this.width = width
        this.height = height
        this.data = new Uint8ClampedArray(width * height * 4)
    }
}

global.ImageData = MockImageData as any

describe('DrawingCanvas', () => {
    let drawingStore: ReturnType<typeof useDrawingStore>
    let mockContext: any

    beforeEach(() => {
        // Setup Pinia
        const pinia = createPinia()
        setActivePinia(pinia)
        drawingStore = useDrawingStore()

        // Mock canvas context
        mockContext = {
            lineCap: 'round',
            lineJoin: 'round',
            imageSmoothingEnabled: true
        }

            // Setup CanvasUtils mocks
            ; (CanvasUtils.initializeCanvas as MockedFunction<any>).mockReturnValue(mockContext)
            ; (CanvasUtils.getCanvasBounds as MockedFunction<any>).mockReturnValue({
                width: 800,
                height: 600,
                top: 0,
                left: 0
            })
            ; (CanvasUtils.screenToCanvasCoordinates as MockedFunction<any>).mockReturnValue({
                x: 100,
                y: 100
            })
            ; (CanvasUtils.getImageData as MockedFunction<any>).mockReturnValue(new MockImageData(800, 600))

        vi.clearAllMocks()
    })

    describe('Component Props and Computed', () => {
        it('should have correct default props', () => {
            expect(DrawingCanvas).toBeDefined()
        })

        it('should compute canvas dimensions correctly', () => {
            // Test that the component can be imported and has the expected structure
            expect(typeof DrawingCanvas).toBe('object')
        })
    })

    describe('Store Integration', () => {
        it('should integrate with drawing store', () => {
            drawingStore.setTool('brush')
            expect(drawingStore.currentTool).toBe('brush')

            drawingStore.setColor('#FF0000')
            expect(drawingStore.currentColor).toBe('#FF0000')

            drawingStore.setBrushSize(10)
            expect(drawingStore.brushSize).toBe(10)
        })

        it('should handle drawing state changes', () => {
            expect(drawingStore.isDrawing).toBe(false)

            drawingStore.setDrawingState(true)
            expect(drawingStore.isDrawing).toBe(true)

            drawingStore.setDrawingState(false)
            expect(drawingStore.isDrawing).toBe(false)
        })
    })

    describe('Canvas Utils Integration', () => {
        it('should call CanvasUtils methods correctly', () => {
            // Test that CanvasUtils methods are properly mocked
            expect(CanvasUtils.initializeCanvas).toBeDefined()
            expect(CanvasUtils.startDrawing).toBeDefined()
            expect(CanvasUtils.continueDrawing).toBeDefined()
            expect(CanvasUtils.endDrawing).toBeDefined()
            expect(CanvasUtils.eraseAt).toBeDefined()
            expect(CanvasUtils.clearCanvas).toBeDefined()
            expect(CanvasUtils.flipCanvas).toBeDefined()
            expect(CanvasUtils.getImageData).toBeDefined()
            expect(CanvasUtils.setImageData).toBeDefined()
        })

        it('should handle coordinate conversion', () => {
            const result = CanvasUtils.screenToCanvasCoordinates(100, 100, {} as HTMLCanvasElement)
            expect(result).toEqual({ x: 100, y: 100 })
        })
    })

    describe('Component Structure', () => {
        it('should be a valid Vue component', () => {
            expect(DrawingCanvas).toBeDefined()
            expect(typeof DrawingCanvas).toBe('object')
        })
    })

    describe('Tool Settings', () => {
        it('should handle tool settings correctly', () => {
            drawingStore.setTool('brush')
            drawingStore.setColor('#FF0000')
            drawingStore.setBrushSize(10)

            const settings = drawingStore.currentToolSettings
            expect(settings.color).toBe('#FF0000')
            expect(settings.size).toBe(10)
            expect(settings.opacity).toBe(1)
        })

        it('should handle eraser tool settings', () => {
            drawingStore.setTool('eraser')
            drawingStore.setBrushSize(15)

            const settings = drawingStore.currentToolSettings
            expect(settings.color).toBe('#FFFFFF')
            expect(settings.size).toBe(15)
        })
    })

    describe('Error Handling', () => {
        it('should handle null canvas context', () => {
            ; (CanvasUtils.initializeCanvas as MockedFunction<any>).mockReturnValue(null)

            // Should not throw when canvas context is null
            expect(() => {
                CanvasUtils.initializeCanvas(null as any, 800, 600)
            }).not.toThrow()
        })

        it('should handle invalid coordinates', () => {
            const result = CanvasUtils.screenToCanvasCoordinates(-100, -100, {} as HTMLCanvasElement)
            expect(result).toEqual({ x: 100, y: 100 }) // Mocked return value
        })
    })

    describe('ImageData Handling', () => {
        it('should work with mock ImageData', () => {
            const imageData = new MockImageData(100, 100)
            expect(imageData.width).toBe(100)
            expect(imageData.height).toBe(100)
            expect(imageData.data).toBeInstanceOf(Uint8ClampedArray)
        })

        it('should handle ImageData operations', () => {
            const imageData = new MockImageData(800, 600)
            CanvasUtils.setImageData(mockContext, imageData as any)
            expect(CanvasUtils.setImageData).toHaveBeenCalledWith(mockContext, imageData)
        })
    })
})