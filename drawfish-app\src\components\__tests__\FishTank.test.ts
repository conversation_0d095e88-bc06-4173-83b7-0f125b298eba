import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import FishTank from '../FishTank.vue'

describe('FishTank', () => {
  it('renders with default dimensions', () => {
    const wrapper = mount(FishTank)
    const tankElement = wrapper.find('.fish-tank')
    
    expect(tankElement.exists()).toBe(true)
    expect(tankElement.attributes('style')).toContain('width: 800px')
    expect(tankElement.attributes('style')).toContain('height: 600px')
  })

  it('renders with custom dimensions', () => {
    const wrapper = mount(FishTank, {
      props: {
        width: 1000,
        height: 700
      }
    })
    
    const tankElement = wrapper.find('.fish-tank')
    expect(tankElement.attributes('style')).toContain('width: 1000px')
    expect(tankElement.attributes('style')).toContain('height: 700px')
  })

  it('renders all visual elements', () => {
    const wrapper = mount(FishTank)
    
    // 检查玻璃边框
    expect(wrapper.find('.tank-frame').exists()).toBe(true)
    expect(wrapper.find('.glass-top').exists()).toBe(true)
    expect(wrapper.find('.glass-bottom').exists()).toBe(true)
    expect(wrapper.find('.glass-left').exists()).toBe(true)
    expect(wrapper.find('.glass-right').exists()).toBe(true)
    
    // 检查水背景和波纹
    expect(wrapper.find('.water-background').exists()).toBe(true)
    expect(wrapper.find('.water-ripples').exists()).toBe(true)
    expect(wrapper.findAll('.ripple')).toHaveLength(3)
    
    // 检查装饰元素
    expect(wrapper.find('.decorations').exists()).toBe(true)
    expect(wrapper.findAll('.seaweed')).toHaveLength(2)
    expect(wrapper.find('.bubbles').exists()).toBe(true)
    expect(wrapper.findAll('.bubble')).toHaveLength(5)
    expect(wrapper.find('.sand-bottom').exists()).toBe(true)
  })

  it('renders seaweed with correct strands', () => {
    const wrapper = mount(FishTank)
    
    const leftSeaweed = wrapper.find('.seaweed-left')
    const rightSeaweed = wrapper.find('.seaweed-right')
    
    expect(leftSeaweed.exists()).toBe(true)
    expect(rightSeaweed.exists()).toBe(true)
    
    // 左侧海草应该有3根
    expect(leftSeaweed.findAll('.seaweed-strand')).toHaveLength(3)
    // 右侧海草应该有2根
    expect(rightSeaweed.findAll('.seaweed-strand')).toHaveLength(2)
  })

  it('renders bubbles with different sizes', () => {
    const wrapper = mount(FishTank)
    
    const bubbles = wrapper.findAll('.bubble')
    expect(bubbles).toHaveLength(5)
    
    // 检查每个气泡都有唯一的类名
    expect(wrapper.find('.bubble-1').exists()).toBe(true)
    expect(wrapper.find('.bubble-2').exists()).toBe(true)
    expect(wrapper.find('.bubble-3').exists()).toBe(true)
    expect(wrapper.find('.bubble-4').exists()).toBe(true)
    expect(wrapper.find('.bubble-5').exists()).toBe(true)
  })

  it('provides slot for content', () => {
    const wrapper = mount(FishTank, {
      slots: {
        default: '<div class="test-content">Test Fish</div>'
      }
    })
    
    expect(wrapper.find('.tank-content').exists()).toBe(true)
    expect(wrapper.find('.test-content').exists()).toBe(true)
    expect(wrapper.find('.test-content').text()).toBe('Test Fish')
  })

  it('has proper CSS classes for responsive design', () => {
    const wrapper = mount(FishTank)
    
    // 检查主要的CSS类是否存在
    expect(wrapper.find('.fish-tank').exists()).toBe(true)
    expect(wrapper.find('.water-background').exists()).toBe(true)
    expect(wrapper.find('.decorations').exists()).toBe(true)
    expect(wrapper.find('.tank-content').exists()).toBe(true)
  })

  it('applies correct styling structure', () => {
    const wrapper = mount(FishTank)
    const tankElement = wrapper.find('.fish-tank')
    
    // 检查基本样式属性
    expect(tankElement.classes()).toContain('fish-tank')
    
    // 检查内容区域的z-index层级
    const contentArea = wrapper.find('.tank-content')
    expect(contentArea.exists()).toBe(true)
  })

  it('maintains proper element hierarchy', () => {
    const wrapper = mount(FishTank)
    
    // 检查元素层级结构
    const tank = wrapper.find('.fish-tank')
    expect(tank.find('.tank-frame').exists()).toBe(true)
    expect(tank.find('.water-background').exists()).toBe(true)
    expect(tank.find('.decorations').exists()).toBe(true)
    expect(tank.find('.tank-content').exists()).toBe(true)
    
    // 检查装饰元素是否在正确的容器内
    const decorations = tank.find('.decorations')
    expect(decorations.find('.seaweed').exists()).toBe(true)
    expect(decorations.find('.bubbles').exists()).toBe(true)
    expect(decorations.find('.sand-bottom').exists()).toBe(true)
  })
})