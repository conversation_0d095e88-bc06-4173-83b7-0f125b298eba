<template>
  <div ref="fishContainer" class="fish-outline" :style="fishStyle" @click="onFishClick">
    <!-- Fish stroke canvas -->
    <canvas ref="fishCanvas" class="fish-canvas" :width="localFishData.dimensions.width"
      :height="localFishData.dimensions.height" />

    <!-- Fish name display -->
    <div v-if="showName && localFishData.name" class="fish-name" :class="{ 'name-visible': nameVisible }">
      {{ localFishData.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import type { FishData, CanvasBounds, FishStroke } from '../types'
import { FishAnimator } from '../utils/fishAnimator'

interface Props {
  fishData: FishData
  bounds: CanvasBounds
  autoStart?: boolean
  showName?: boolean
  interactive?: boolean
}

interface Emits {
  (e: 'update:fishData', fishData: FishData): void
  (e: 'fishClick', fishData: FishData): void
  (e: 'animationStart'): void
  (e: 'animationStop'): void
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: true,
  showName: true,
  interactive: false
})

const emit = defineEmits<Emits>()

// Template refs
const fishContainer = ref<HTMLDivElement>()
const fishCanvas = ref<HTMLCanvasElement>()

// Component state
const animator = ref<FishAnimator>()
const nameVisible = ref(false)
const isAnimating = ref(false)
const localFishData = ref<FishData>({ ...props.fishData })

// Force reactivity trigger
const forceUpdate = ref(0)

// Computed styles for fish positioning and animation
const fishStyle = computed(() => {
  // Force reactivity by accessing forceUpdate
  forceUpdate.value

  const tailOffset = animator.value?.getTailOffset() || 0
  const direction = localFishData.value.animationState.direction
  const position = localFishData.value.position

  return {
    position: 'absolute' as const,
    left: `${position.x}px`,
    top: `${position.y}px`,
    transform: `scaleX(${direction}) skewX(${tailOffset * 0.05}deg)`,
    transition: isAnimating.value ? 'none' : 'transform 0.2s ease',
    cursor: props.interactive ? 'pointer' : 'default',
    zIndex: 10,
    transformOrigin: 'center center'
  }
})

// Draw fish strokes on canvas
const drawFishStrokes = (ctx: CanvasRenderingContext2D, strokes: FishStroke[]): void => {
  ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height)

  if (!strokes || strokes.length === 0) {
    drawPlaceholderFish(ctx)
    return
  }

  // Set common style properties
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  // Draw each stroke
  for (const stroke of strokes) {
    if (stroke.points.length < 2) continue

    ctx.strokeStyle = stroke.color
    ctx.lineWidth = stroke.size

    ctx.beginPath()
    ctx.moveTo(stroke.points[0].x, stroke.points[0].y)

    for (let i = 1; i < stroke.points.length; i++) {
      ctx.lineTo(stroke.points[i].x, stroke.points[i].y)
    }

    ctx.stroke()
  }

  // Add a subtle glow effect for better visibility in water
  ctx.shadowColor = 'rgba(33, 150, 243, 0.3)'
  ctx.shadowBlur = 2

  // Redraw strokes with glow
  for (const stroke of strokes) {
    if (stroke.points.length < 2) continue

    ctx.strokeStyle = stroke.color
    ctx.lineWidth = stroke.size

    ctx.beginPath()
    ctx.moveTo(stroke.points[0].x, stroke.points[0].y)

    for (let i = 1; i < stroke.points.length; i++) {
      ctx.lineTo(stroke.points[i].x, stroke.points[i].y)
    }

    ctx.stroke()
  }

  // Reset shadow
  ctx.shadowColor = 'transparent'
  ctx.shadowBlur = 0
}

// Draw a simple placeholder fish when no strokes are available
const drawPlaceholderFish = (ctx: CanvasRenderingContext2D): void => {
  const { width, height } = localFishData.value.dimensions

  ctx.clearRect(0, 0, width, height)

  // Fish body outline
  ctx.strokeStyle = '#4A90E2'
  ctx.lineWidth = 3
  ctx.lineCap = 'round'
  ctx.lineJoin = 'round'

  // Fish body (ellipse outline)
  ctx.beginPath()
  ctx.ellipse(width * 0.4, height * 0.5, width * 0.3, height * 0.3, 0, 0, Math.PI * 2)
  ctx.stroke()

  // Fish tail outline
  ctx.beginPath()
  ctx.moveTo(width * 0.1, height * 0.5)
  ctx.lineTo(0, height * 0.3)
  ctx.lineTo(0, height * 0.7)
  ctx.closePath()
  ctx.stroke()

  // Fish eye
  ctx.fillStyle = '#4A90E2'
  ctx.beginPath()
  ctx.arc(width * 0.5, height * 0.4, width * 0.06, 0, Math.PI * 2)
  ctx.fill()
}

// Initialize fish canvas with stroke data
const initializeFishCanvas = async (): Promise<void> => {
  if (!fishCanvas.value) return

  const canvas = fishCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  try {
    // Draw using stroke data if available
    if (localFishData.value.strokes && localFishData.value.strokes.length > 0) {
      console.log(`Drawing ${localFishData.value.strokes.length} strokes`)
      drawFishStrokes(ctx, localFishData.value.strokes)
    } else {
      console.log('No strokes available, drawing placeholder')
      // Fallback to placeholder fish
      drawPlaceholderFish(ctx)
    }
  } catch (error) {
    console.error('Failed to draw fish strokes:', error)
    drawPlaceholderFish(ctx)
  }
}

// Handle fish click
const onFishClick = (): void => {
  if (props.interactive) {
    emit('fishClick', localFishData.value)

    // Show name temporarily on click
    if (props.showName && localFishData.value.name) {
      showNameTemporarily()
    }
  }
}

// Show fish name temporarily
const showNameTemporarily = (): void => {
  nameVisible.value = true
  setTimeout(() => {
    nameVisible.value = false
  }, 2000)
}

// Start animation
const startAnimation = (): void => {
  if (animator.value && !isAnimating.value) {
    animator.value.startAnimation()
    isAnimating.value = true
    emit('animationStart')
  }
}

// Stop animation
const stopAnimation = (): void => {
  if (animator.value && isAnimating.value) {
    animator.value.stopAnimation()
    isAnimating.value = false
    emit('animationStop')
  }
}

// Pause animation
const pauseAnimation = (): void => {
  if (animator.value) {
    animator.value.pause()
    isAnimating.value = false
  }
}

// Resume animation
const resumeAnimation = (): void => {
  if (animator.value) {
    animator.value.resume()
    isAnimating.value = true
  }
}

// Update bounds (for window resize)
const updateBounds = (newBounds: CanvasBounds): void => {
  if (animator.value) {
    animator.value.updateBounds(newBounds)
  }
}

// Watch for bounds changes
watch(() => props.bounds, (newBounds) => {
  updateBounds(newBounds)
}, { deep: true })

// Watch for position changes in local fish data
watch(() => localFishData.value.position, (newPosition, oldPosition) => {
  if (oldPosition && (newPosition.x !== oldPosition.x || newPosition.y !== oldPosition.y)) {
    console.log('Position changed:', { from: oldPosition, to: newPosition })
  }
}, { deep: true })

// Watch for fish data changes from props
watch(() => props.fishData, async (newFishData) => {
  localFishData.value = { ...newFishData }

  if (animator.value) {
    // Update animator with new fish data
    animator.value.reset(newFishData)
  }

  // Reinitialize canvas with new stroke data
  await nextTick()
  await initializeFishCanvas()
}, { deep: true })

// Component lifecycle
onMounted(async () => {
  // Initialize local fish data
  localFishData.value = { ...props.fishData }

  await nextTick()
  await initializeFishCanvas()

  // Create animator
  animator.value = new FishAnimator(
    localFishData.value,
    props.bounds,
    (updatedFishData) => {
      // Debug: Log position updates more frequently
      if (Math.random() < 0.1) { // Log ~10% of updates
        console.log('Fish position updated:', {
          position: updatedFishData.position,
          velocity: updatedFishData.velocity,
          direction: updatedFishData.animationState.direction
        })
      }

      // Update local fish data
      localFishData.value = updatedFishData

      // Force reactivity update
      forceUpdate.value++

      emit('update:fishData', updatedFishData)
    }
  )

  // Auto-start animation if enabled
  if (props.autoStart) {
    startAnimation()
  }

  // Show name initially if enabled
  if (props.showName && props.fishData.name) {
    setTimeout(() => {
      showNameTemporarily()
    }, 500)
  }

  // Debug: Add a simple test animation to verify position updates work
  setTimeout(() => {
    console.log('Starting debug position test...')
    let testX = localFishData.value.position.x
    const testInterval = setInterval(() => {
      testX += 5
      if (testX > props.bounds.width - 100) {
        testX = 50
      }

      localFishData.value = {
        ...localFishData.value,
        position: { ...localFishData.value.position, x: testX }
      }
      forceUpdate.value++

      console.log('Debug: Updated position to', testX)
    }, 100)

    // Stop test after 5 seconds
    setTimeout(() => {
      clearInterval(testInterval)
      console.log('Debug position test completed')
    }, 5000)
  }, 2000)
})

onUnmounted(() => {
  if (animator.value) {
    animator.value.destroy()
  }
})

// Expose methods for parent components
defineExpose({
  startAnimation,
  stopAnimation,
  pauseAnimation,
  resumeAnimation,
  updateBounds,
  isAnimating: () => isAnimating.value,
  getAnimator: () => animator.value
})
</script>

<style scoped>
.fish-outline {
  pointer-events: auto;
  user-select: none;
}

.fish-canvas {
  display: block;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

.fish-name {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 20;
}

.fish-name.name-visible {
  opacity: 1;
}

.fish-outline:hover .fish-name {
  opacity: 1;
}

/* Animation effects */
.fish-outline {
  will-change: transform;
}

/* Interactive fish effects */
.fish-outline[style*="cursor: pointer"]:hover {
  filter: brightness(1.2) drop-shadow(0 0 8px rgba(33, 150, 243, 0.5));
}

.fish-outline[style*="cursor: pointer"]:active {
  transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fish-name {
    font-size: 10px;
    padding: 2px 6px;
    top: -25px;
  }
}
</style>