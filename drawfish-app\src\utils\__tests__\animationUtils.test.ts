import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  calculatePosition,
  checkBoundaries,
  updateTailAnimation,
  calculateTailOffset,
  createAnimationLoop,
  generateRandomVelocity,
  interpolatePosition,
  calculateDistance,
  ANIMATION_CONFIG
} from '../animationUtils'
import type { Position, Velocity, Dimensions, CanvasBounds } from '../../types'

describe('animationUtils', () => {
  describe('calculatePosition', () => {
    it('should calculate new position based on velocity and delta time', () => {
      const currentPos: Position = { x: 100, y: 100 }
      const velocity: Velocity = { x: 2, y: 1 }
      const deltaTime = ANIMATION_CONFIG.FRAME_TIME // One frame
      
      const newPos = calculatePosition(currentPos, velocity, deltaTime)
      
      expect(newPos.x).toBe(102)
      expect(newPos.y).toBe(101)
    })
    
    it('should handle large delta times by normalizing them', () => {
      const currentPos: Position = { x: 100, y: 100 }
      const velocity: Velocity = { x: 2, y: 1 }
      const largeDeltaTime = ANIMATION_CONFIG.FRAME_TIME * 5 // Very large delta
      
      const newPos = calculatePosition(currentPos, velocity, largeDeltaTime)
      
      // Should be clamped to max 2 frames worth of movement
      expect(newPos.x).toBe(104) // 2 * 2 frames
      expect(newPos.y).toBe(102) // 1 * 2 frames
    })
    
    it('should handle zero delta time', () => {
      const currentPos: Position = { x: 100, y: 100 }
      const velocity: Velocity = { x: 2, y: 1 }
      const deltaTime = 0
      
      const newPos = calculatePosition(currentPos, velocity, deltaTime)
      
      expect(newPos.x).toBe(100)
      expect(newPos.y).toBe(100)
    })
  })
  
  describe('checkBoundaries', () => {
    const bounds: CanvasBounds = {
      width: 800,
      height: 600,
      top: 0,
      left: 0
    }
    const fishDimensions: Dimensions = { width: 50, height: 30 }
    
    it('should bounce off left boundary', () => {
      const position: Position = { x: 10, y: 300 }
      
      const result = checkBoundaries(position, bounds, fishDimensions)
      
      expect(result.position.x).toBe(ANIMATION_CONFIG.BOUNDARY_PADDING)
      expect(result.velocity.x).toBeGreaterThan(0) // Should be positive (moving right)
    })
    
    it('should bounce off right boundary', () => {
      const position: Position = { x: 780, y: 300 }
      
      const result = checkBoundaries(position, bounds, fishDimensions)
      
      expect(result.position.x).toBe(bounds.width - fishDimensions.width - ANIMATION_CONFIG.BOUNDARY_PADDING)
      expect(result.velocity.x).toBeLessThan(0) // Should be negative (moving left)
    })
    
    it('should bounce off top boundary', () => {
      const position: Position = { x: 400, y: 10 }
      
      const result = checkBoundaries(position, bounds, fishDimensions)
      
      expect(result.position.y).toBe(ANIMATION_CONFIG.BOUNDARY_PADDING)
      expect(result.velocity.y).toBeGreaterThan(0) // Should be positive (moving down)
    })
    
    it('should bounce off bottom boundary', () => {
      const position: Position = { x: 400, y: 580 }
      
      const result = checkBoundaries(position, bounds, fishDimensions)
      
      expect(result.position.y).toBe(bounds.height - fishDimensions.height - ANIMATION_CONFIG.BOUNDARY_PADDING)
      expect(result.velocity.y).toBeLessThan(0) // Should be negative (moving up)
    })
    
    it('should not change position when within bounds', () => {
      const position: Position = { x: 400, y: 300 }
      
      const result = checkBoundaries(position, bounds, fishDimensions)
      
      expect(result.position.x).toBe(400)
      expect(result.position.y).toBe(300)
      expect(result.velocity.x).toBe(ANIMATION_CONFIG.DEFAULT_VELOCITY.x)
      expect(result.velocity.y).toBe(ANIMATION_CONFIG.DEFAULT_VELOCITY.y)
    })
  })
  
  describe('updateTailAnimation', () => {
    it('should increment tail phase by default speed', () => {
      const currentPhase = 0
      const newPhase = updateTailAnimation(currentPhase)
      
      expect(newPhase).toBe(ANIMATION_CONFIG.TAIL_SPEED)
    })
    
    it('should increment tail phase by custom speed', () => {
      const currentPhase = 0
      const speed = 2
      const newPhase = updateTailAnimation(currentPhase, speed)
      
      expect(newPhase).toBe(ANIMATION_CONFIG.TAIL_SPEED * speed)
    })
    
    it('should wrap phase around to prevent overflow', () => {
      const currentPhase = Math.PI * 4 - 0.1
      const newPhase = updateTailAnimation(currentPhase)
      
      expect(newPhase).toBeLessThan(Math.PI * 4)
      expect(newPhase).toBeGreaterThan(0)
    })
  })
  
  describe('calculateTailOffset', () => {
    it('should calculate tail offset using sine wave', () => {
      const phase = Math.PI / 2 // 90 degrees
      const offset = calculateTailOffset(phase)
      
      expect(offset).toBeCloseTo(ANIMATION_CONFIG.TAIL_AMPLITUDE)
    })
    
    it('should use custom amplitude', () => {
      const phase = Math.PI / 2
      const customAmplitude = 20
      const offset = calculateTailOffset(phase, customAmplitude)
      
      expect(offset).toBeCloseTo(customAmplitude)
    })
    
    it('should return zero at phase 0', () => {
      const phase = 0
      const offset = calculateTailOffset(phase)
      
      expect(offset).toBeCloseTo(0)
    })
  })
  
  describe('createAnimationLoop', () => {
    beforeEach(() => {
      vi.useFakeTimers()
      global.requestAnimationFrame = vi.fn((callback) => {
        return setTimeout(callback, 16) as unknown as number
      })
      global.cancelAnimationFrame = vi.fn((id) => {
        clearTimeout(id)
      })
      global.performance = {
        now: vi.fn(() => Date.now())
      } as any
    })
    
    afterEach(() => {
      vi.useRealTimers()
      vi.restoreAllMocks()
    })
    
    it('should create animation loop that can be started and stopped', () => {
      const callback = vi.fn()
      const loop = createAnimationLoop(callback)
      
      expect(loop.isRunning()).toBe(false)
      
      loop.start()
      expect(loop.isRunning()).toBe(true)
      
      loop.stop()
      expect(loop.isRunning()).toBe(false)
    })
    
    it('should call callback with delta time', () => {
      const callback = vi.fn()
      const loop = createAnimationLoop(callback)
      
      let rafCallback: ((time: number) => void) | null = null
      vi.mocked(requestAnimationFrame).mockImplementation((cb) => {
        rafCallback = cb
        return 1
      })
      
      vi.mocked(performance.now)
        .mockReturnValueOnce(0)
        .mockReturnValueOnce(16)
      
      loop.start()
      
      // Manually trigger the animation frame callback
      if (rafCallback) {
        rafCallback(16)
      }
      
      expect(callback).toHaveBeenCalledWith(16)
      
      loop.stop()
    })
    
    it('should not start multiple times', () => {
      const callback = vi.fn()
      const loop = createAnimationLoop(callback)
      
      loop.start()
      loop.start() // Should not start again
      
      expect(requestAnimationFrame).toHaveBeenCalledTimes(1)
      
      loop.stop()
    })
  })
  
  describe('generateRandomVelocity', () => {
    it('should generate velocity within specified bounds', () => {
      const minSpeed = 1
      const maxSpeed = 3
      
      for (let i = 0; i < 10; i++) {
        const velocity = generateRandomVelocity(minSpeed, maxSpeed)
        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
        
        expect(speed).toBeGreaterThanOrEqual(minSpeed)
        expect(speed).toBeLessThanOrEqual(maxSpeed)
      }
    })
    
    it('should use default bounds when not specified', () => {
      const velocity = generateRandomVelocity()
      const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y)
      
      expect(speed).toBeGreaterThanOrEqual(1)
      expect(speed).toBeLessThanOrEqual(3)
    })
  })
  
  describe('interpolatePosition', () => {
    it('should interpolate between two positions', () => {
      const from: Position = { x: 0, y: 0 }
      const to: Position = { x: 100, y: 100 }
      const factor = 0.5
      
      const result = interpolatePosition(from, to, factor)
      
      expect(result.x).toBe(50)
      expect(result.y).toBe(50)
    })
    
    it('should clamp factor to 0-1 range', () => {
      const from: Position = { x: 0, y: 0 }
      const to: Position = { x: 100, y: 100 }
      
      const resultNegative = interpolatePosition(from, to, -0.5)
      expect(resultNegative.x).toBe(0)
      expect(resultNegative.y).toBe(0)
      
      const resultOver = interpolatePosition(from, to, 1.5)
      expect(resultOver.x).toBe(100)
      expect(resultOver.y).toBe(100)
    })
  })
  
  describe('calculateDistance', () => {
    it('should calculate distance between two positions', () => {
      const pos1: Position = { x: 0, y: 0 }
      const pos2: Position = { x: 3, y: 4 }
      
      const distance = calculateDistance(pos1, pos2)
      
      expect(distance).toBe(5) // 3-4-5 triangle
    })
    
    it('should return zero for same positions', () => {
      const pos1: Position = { x: 100, y: 100 }
      const pos2: Position = { x: 100, y: 100 }
      
      const distance = calculateDistance(pos1, pos2)
      
      expect(distance).toBe(0)
    })
  })
})