<template>
  <div class="color-picker">
    <div class="current-color-section">
      <label class="current-color-label">当前颜色:</label>
      <div 
        class="current-color-display"
        :style="{ backgroundColor: currentColor }"
        :title="`当前颜色: ${currentColor}`"
      ></div>
    </div>
    
    <div class="preset-colors">
      <button
        v-for="color in presetColors"
        :key="color"
        class="color-button"
        :class="{ active: currentColor === color }"
        :style="{ backgroundColor: color }"
        :title="`选择颜色: ${color}`"
        @click="selectColor(color)"
        :aria-label="`选择颜色 ${color}`"
      ></button>
    </div>
    
    <div class="custom-color-section">
      <label for="custom-color-input" class="custom-color-label">自定义颜色:</label>
      <input
        id="custom-color-input"
        type="color"
        class="custom-color-input"
        :value="currentColor"
        @input="onCustomColorChange"
        aria-label="自定义颜色选择器"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useDrawingStore } from '@/stores/drawingStore'

// Props (none needed for this component)
interface Props {}
defineProps<Props>()

// Emits (none needed as we use store directly)
interface Emits {}
defineEmits<Emits>()

// Store
const drawingStore = useDrawingStore()

// Computed
const currentColor = computed(() => drawingStore.currentColor)

// Preset colors array
const presetColors = [
  '#000000', // Black
  '#FFFFFF', // White
  '#FF0000', // Red
  '#00FF00', // Green
  '#0000FF', // Blue
  '#FFFF00', // Yellow
  '#FF00FF', // Magenta
  '#00FFFF', // Cyan
  '#FFA500', // Orange
  '#800080', // Purple
  '#FFC0CB', // Pink
  '#A52A2A', // Brown
  '#808080', // Gray
  '#90EE90', // Light Green
  '#87CEEB', // Sky Blue
  '#DDA0DD'  // Plum
]

// Methods
function selectColor(color: string) {
  drawingStore.setColor(color)
}

function onCustomColorChange(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.value) {
    drawingStore.setColor(target.value)
  }
}
</script>

<style scoped>
.color-picker {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.current-color-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-color-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  min-width: 70px;
}

.current-color-display {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 2px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.current-color-display:hover {
  transform: scale(1.05);
}

.preset-colors {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 6px;
  padding: 8px 0;
}

.color-button {
  width: 28px;
  height: 28px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.color-button:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.color-button.active {
  border-color: #007bff;
  border-width: 3px;
  transform: scale(1.05);
}

.color-button.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.custom-color-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.custom-color-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  min-width: 80px;
}

.custom-color-input {
  width: 40px;
  height: 32px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.custom-color-input:hover {
  border-color: #007bff;
}

.custom-color-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .color-picker {
    padding: 12px;
  }
  
  .preset-colors {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .current-color-section,
  .custom-color-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .current-color-label,
  .custom-color-label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .preset-colors {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>