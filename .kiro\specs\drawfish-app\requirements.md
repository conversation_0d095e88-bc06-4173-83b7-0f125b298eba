# 需求文档

## 介绍

DrawFish是一个互动绘画网站，用户可以使用各种绘画工具在画布上绘制鱼类，然后观看他们绘制的鱼在虚拟水族箱中游泳。该应用使用Vue3+Vite项目结构，配置Vue Router路由，使用Pinia状态管理器和Axios请求库。

## 需求

### 需求1：绘画工具栏

**用户故事：** 作为用户，我想要使用各种绘画工具来绘制我的鱼，以便我可以创造出独特的鱼类设计。

#### 验收标准

1. WHEN 用户访问首页 THEN 系统 SHALL 显示包含颜色选择器的工具栏
2. WHEN 用户点击颜色按钮 THEN 系统 SHALL 将当前绘画颜色设置为所选颜色
3. WHEN 用户点击擦除工具 THEN 系统 SHALL 切换到擦除模式
4. WHEN 用户调整线条粗细滑块 THEN 系统 SHALL 更新当前画笔大小
5. WHEN 用户点击撤销按钮 THEN 系统 SHALL 撤销最后一次绘画操作
6. WHEN 用户点击清除按钮 THEN 系统 SHALL 清空整个画布
7. WHEN 用户点击翻转按钮 THEN 系统 SHALL 水平翻转画布内容

### 需求2：画布绘画功能

**用户故事：** 作为用户，我想要在画布上自由绘画，以便我可以创作出我想要的鱼类形状。

#### 验收标准

1. WHEN 用户在画布上按下鼠标 THEN 系统 SHALL 开始记录绘画路径
2. WHEN 用户拖动鼠标 THEN 系统 SHALL 实时显示绘画轨迹
3. WHEN 用户释放鼠标 THEN 系统 SHALL 完成当前绘画笔画
4. WHEN 用户使用擦除工具 THEN 系统 SHALL 擦除接触到的画布内容
5. WHEN 用户绘画时 THEN 系统 SHALL 使用当前选择的颜色和线条粗细
6. WHEN 画布有内容时 THEN 系统 SHALL 启用"make it swim"按钮

### 需求3：鱼类命名和保存

**用户故事：** 作为用户，我想要给我绘制的鱼命名，以便我可以个性化我的创作。

#### 验收标准

1. WHEN 用户点击"make it swim"按钮 THEN 系统 SHALL 显示命名对话框
2. WHEN 对话框显示时 THEN 系统 SHALL 提供文本输入框供用户输入鱼的名字
3. WHEN 用户输入名字并确认 THEN 系统 SHALL 保存鱼的绘画数据和名字
4. WHEN 用户取消命名 THEN 系统 SHALL 关闭对话框并返回绘画页面
5. WHEN 鱼被成功命名 THEN 系统 SHALL 跳转到游泳页面

### 需求4：鱼类游泳动画

**用户故事：** 作为用户，我想要看到我绘制的鱼在水中游泳，以便我可以享受我创作的成果。

#### 验收标准

1. WHEN 用户跳转到游泳页面 THEN 系统 SHALL 显示用户绘制的鱼
2. WHEN 鱼显示在游泳页面 THEN 系统 SHALL 播放尾巴摆动动画
3. WHEN 鱼在游泳时 THEN 系统 SHALL 让鱼在屏幕范围内自由移动
4. WHEN 鱼游泳时 THEN 系统 SHALL 显示鱼的名字
5. WHEN 鱼接近屏幕边缘 THEN 系统 SHALL 让鱼转向并继续游泳
6. WHEN 用户在游泳页面 THEN 系统 SHALL 提供返回绘画页面的选项

### 需求5：路由和状态管理

**用户故事：** 作为用户，我想要在不同页面间流畅导航，以便我可以轻松使用应用的各项功能。

#### 验收标准

1. WHEN 应用启动 THEN 系统 SHALL 显示绘画首页
2. WHEN 用户完成鱼类命名 THEN 系统 SHALL 使用Vue Router导航到游泳页面
3. WHEN 页面切换时 THEN 系统 SHALL 使用Pinia保持鱼类数据状态
4. WHEN 用户返回绘画页面 THEN 系统 SHALL 保留之前的绘画工具设置
5. WHEN 需要数据持久化时 THEN 系统 SHALL 使用Axios进行API请求

### 需求6：响应式设计

**用户故事：** 作为用户，我想要在不同设备上都能正常使用应用，以便我可以随时随地绘制和观看我的鱼。

#### 验收标准

1. WHEN 用户在移动设备上访问 THEN 系统 SHALL 适配触摸操作
2. WHEN 屏幕尺寸改变 THEN 系统 SHALL 调整画布和工具栏布局
3. WHEN 在小屏幕设备上 THEN 系统 SHALL 保持所有功能的可用性
4. WHEN 用户使用触摸设备 THEN 系统 SHALL 支持触摸绘画操作