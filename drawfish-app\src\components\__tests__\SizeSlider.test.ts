import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import SizeSlider from '../SizeSlider.vue'
import { useDrawingStore } from '@/stores/drawingStore'

describe('SizeSlider', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders correctly with default state', () => {
    const wrapper = mount(SizeSlider)
    
    // Check if component renders
    expect(wrapper.find('.size-slider').exists()).toBe(true)
    
    // Check if header section exists
    expect(wrapper.find('.size-slider-header').exists()).toBe(true)
    expect(wrapper.find('.size-label').exists()).toBe(true)
    expect(wrapper.find('.size-display').exists()).toBe(true)
    
    // Check if slider input exists
    expect(wrapper.find('.size-slider-input').exists()).toBe(true)
    
    // Check if preview section exists
    expect(wrapper.find('.size-preview').exists()).toBe(true)
    expect(wrapper.find('.size-preview-dot').exists()).toBe(true)
    
    // Check if quick size buttons exist
    expect(wrapper.find('.quick-sizes').exists()).toBe(true)
    const quickButtons = wrapper.findAll('.quick-size-button')
    expect(quickButtons.length).toBe(5) // Should have 5 quick size buttons
  })

  it('displays current size from store', () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    
    // Default size should be 5
    expect(wrapper.find('.size-value').text()).toBe('5')
    
    // Change size in store
    store.setBrushSize(15)
    
    // Wait for reactivity
    wrapper.vm.$nextTick(() => {
      expect(wrapper.find('.size-value').text()).toBe('15')
    })
  })

  it('updates slider input value when store changes', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    
    // Change size in store
    store.setBrushSize(25)
    await wrapper.vm.$nextTick()
    
    const sliderInput = wrapper.find('.size-slider-input')
    expect(sliderInput.element.value).toBe('25')
  })

  it('calls setBrushSize when slider input changes', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    const setBrushSizeSpy = vi.spyOn(store, 'setBrushSize')
    
    const sliderInput = wrapper.find('.size-slider-input')
    
    // Simulate slider input change
    await sliderInput.setValue('20')
    await sliderInput.trigger('input')
    
    expect(setBrushSizeSpy).toHaveBeenCalledWith(20)
  })

  it('emits size-changed event when slider changes', async () => {
    const wrapper = mount(SizeSlider)
    
    const sliderInput = wrapper.find('.size-slider-input')
    
    // Simulate slider input change
    await sliderInput.setValue('30')
    await sliderInput.trigger('input')
    
    expect(wrapper.emitted('size-changed')).toBeTruthy()
    expect(wrapper.emitted('size-changed')?.[0]).toEqual([30])
  })

  it('calls setBrushSize when quick size button is clicked', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    const setBrushSizeSpy = vi.spyOn(store, 'setBrushSize')
    
    // Click on the "10" quick size button
    const quickButtons = wrapper.findAll('.quick-size-button')
    const button10 = quickButtons.find(button => button.text() === '10')
    
    await button10?.trigger('click')
    
    expect(setBrushSizeSpy).toHaveBeenCalledWith(10)
  })

  it('emits size-changed event when quick size button is clicked', async () => {
    const wrapper = mount(SizeSlider)
    
    // Click on the "15" quick size button
    const quickButtons = wrapper.findAll('.quick-size-button')
    const button15 = quickButtons.find(button => button.text() === '15')
    
    await button15?.trigger('click')
    
    expect(wrapper.emitted('size-changed')).toBeTruthy()
    expect(wrapper.emitted('size-changed')?.[0]).toEqual([15])
  })

  it('highlights active quick size button', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    
    // Set size to 10
    store.setBrushSize(10)
    await wrapper.vm.$nextTick()
    
    // Find the "10" button
    const quickButtons = wrapper.findAll('.quick-size-button')
    const button10 = quickButtons.find(button => button.text() === '10')
    
    expect(button10?.classes()).toContain('active')
  })

  it('updates preview dot size based on current size', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    
    // Set size to 20
    store.setBrushSize(20)
    await wrapper.vm.$nextTick()
    
    const previewDot = wrapper.find('.size-preview-dot')
    const style = previewDot.attributes('style')
    
    expect(style).toContain('width: 20px')
    expect(style).toContain('height: 20px')
  })

  it('uses minimum size for very small preview dots', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    
    // Set size to 1 (very small)
    store.setBrushSize(1)
    await wrapper.vm.$nextTick()
    
    const previewDot = wrapper.find('.size-preview-dot')
    const style = previewDot.attributes('style')
    
    // Should use minimum size of 4px for visibility
    expect(style).toContain('width: 4px')
    expect(style).toContain('height: 4px')
  })

  it('respects min and max size props', () => {
    const wrapper = mount(SizeSlider, {
      props: {
        minSize: 2,
        maxSize: 30
      }
    })
    
    const sliderInput = wrapper.find('.size-slider-input')
    expect(sliderInput.attributes('min')).toBe('2')
    expect(sliderInput.attributes('max')).toBe('30')
    
    // Check track labels
    const trackLabels = wrapper.findAll('.track-label')
    expect(trackLabels[0].text()).toBe('2')
    expect(trackLabels[1].text()).toBe('30')
  })

  it('uses custom preview color when provided', () => {
    const wrapper = mount(SizeSlider, {
      props: {
        previewColor: '#FF0000'
      }
    })
    
    const previewDot = wrapper.find('.size-preview-dot')
    const style = previewDot.attributes('style')
    
    expect(style).toContain('background-color: rgb(255, 0, 0)')
  })

  it('has proper accessibility attributes', () => {
    const wrapper = mount(SizeSlider)
    
    // Check slider input accessibility
    const sliderInput = wrapper.find('.size-slider-input')
    expect(sliderInput.attributes('id')).toBe('brush-size-slider')
    expect(sliderInput.attributes('aria-label')).toBeDefined()
    expect(sliderInput.attributes('aria-valuemin')).toBeDefined()
    expect(sliderInput.attributes('aria-valuemax')).toBeDefined()
    expect(sliderInput.attributes('aria-valuenow')).toBeDefined()
    
    // Check label association
    const label = wrapper.find('label[for="brush-size-slider"]')
    expect(label.exists()).toBe(true)
    
    // Check quick size buttons have aria-labels
    const quickButtons = wrapper.findAll('.quick-size-button')
    quickButtons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
      expect(button.attributes('title')).toBeDefined()
    })
  })

  it('handles invalid slider input gracefully', async () => {
    const wrapper = mount(SizeSlider)
    const store = useDrawingStore()
    const setBrushSizeSpy = vi.spyOn(store, 'setBrushSize')
    
    const sliderInput = wrapper.find('.size-slider-input')
    
    // Simulate invalid input (this shouldn't happen with range input, but test anyway)
    const inputElement = sliderInput.element as HTMLInputElement
    inputElement.value = 'invalid'
    await sliderInput.trigger('input')
    
    // setBrushSize should not be called with invalid value
    expect(setBrushSizeSpy).not.toHaveBeenCalledWith(NaN)
  })

  it('displays correct quick size options', () => {
    const wrapper = mount(SizeSlider)
    const quickButtons = wrapper.findAll('.quick-size-button')
    
    const expectedSizes = [2, 5, 10, 15, 25]
    expect(quickButtons.length).toBe(expectedSizes.length)
    
    expectedSizes.forEach((size, index) => {
      expect(quickButtons[index].text()).toBe(size.toString())
    })
  })

  it('validates size range when setting brush size', async () => {
    const wrapper = mount(SizeSlider, {
      props: {
        minSize: 5,
        maxSize: 20
      }
    })
    const store = useDrawingStore()
    const setBrushSizeSpy = vi.spyOn(store, 'setBrushSize')
    
    const sliderInput = wrapper.find('.size-slider-input')
    
    // Try to set size below minimum (this shouldn't happen with range input, but test the validation)
    const inputElement = sliderInput.element as HTMLInputElement
    inputElement.value = '3'
    await sliderInput.trigger('input')
    
    // Should not call setBrushSize with invalid value
    expect(setBrushSizeSpy).not.toHaveBeenCalledWith(3)
  })
})