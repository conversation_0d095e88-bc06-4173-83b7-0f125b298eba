// Type definition tests
import { describe, it, expect } from 'vitest'
import type { 
  FishData, 
  DrawingTool, 
  ToolSettings, 
  Position, 
  Velocity, 
  Dimensions,
  AnimationState,
  DrawingPoint,
  DrawingPath,
  CanvasBounds
} from '../index'

describe('Core Type Definitions', () => {
  it('should define DrawingTool type correctly', () => {
    const brushTool: DrawingTool = 'brush'
    const eraserTool: DrawingTool = 'eraser'
    
    expect(brushTool).toBe('brush')
    expect(eraserTool).toBe('eraser')
  })

  it('should define ToolSettings interface correctly', () => {
    const toolSettings: ToolSettings = {
      color: '#FF0000',
      size: 10,
      opacity: 1.0
    }
    
    expect(toolSettings.color).toBe('#FF0000')
    expect(toolSettings.size).toBe(10)
    expect(toolSettings.opacity).toBe(1.0)
  })

  it('should define Position interface correctly', () => {
    const position: Position = { x: 100, y: 200 }
    
    expect(position.x).toBe(100)
    expect(position.y).toBe(200)
  })

  it('should define FishData interface correctly', () => {
    const fishData: FishData = {
      id: 'fish-123',
      name: 'My Fish',
      imageData: 'base64-data',
      createdAt: new Date(),
      dimensions: { width: 100, height: 50 },
      position: { x: 0, y: 0 },
      velocity: { x: 2, y: 1 },
      animationState: { tailPhase: 0, direction: 1 }
    }
    
    expect(fishData.id).toBe('fish-123')
    expect(fishData.name).toBe('My Fish')
    expect(typeof fishData.createdAt).toBe('object')
    expect(fishData.dimensions.width).toBe(100)
    expect(fishData.position.x).toBe(0)
    expect(fishData.velocity.x).toBe(2)
    expect(fishData.animationState.direction).toBe(1)
  })

  it('should define DrawingPoint interface correctly', () => {
    const point: DrawingPoint = {
      x: 50,
      y: 75,
      pressure: 0.8
    }
    
    expect(point.x).toBe(50)
    expect(point.y).toBe(75)
    expect(point.pressure).toBe(0.8)
  })

  it('should define DrawingPath interface correctly', () => {
    const path: DrawingPath = {
      points: [
        { x: 0, y: 0, pressure: 1 },
        { x: 10, y: 10, pressure: 0.9 }
      ],
      tool: 'brush',
      settings: { color: '#000000', size: 5, opacity: 1 },
      timestamp: Date.now()
    }
    
    expect(path.points).toHaveLength(2)
    expect(path.tool).toBe('brush')
    expect(path.settings.color).toBe('#000000')
    expect(typeof path.timestamp).toBe('number')
  })

  it('should define CanvasBounds interface correctly', () => {
    const bounds: CanvasBounds = {
      width: 800,
      height: 600,
      top: 0,
      left: 0
    }
    
    expect(bounds.width).toBe(800)
    expect(bounds.height).toBe(600)
    expect(bounds.top).toBe(0)
    expect(bounds.left).toBe(0)
  })
})