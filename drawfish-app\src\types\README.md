# DrawFish Type Definitions

This directory contains all TypeScript type definitions for the DrawFish application.

## Files Overview

### `index.ts` - Core Types
- `DrawingTool`: Union type for drawing tools ('brush' | 'eraser')
- `ToolSettings`: Interface for tool configuration (color, size, opacity)
- `FishData`: Main interface for fish data including image, position, animation state
- `Position`, `Velocity`, `Dimensions`: Basic geometric interfaces
- `DrawingPoint`, `DrawingPath`: Drawing operation data structures
- `CanvasBounds`: Canvas boundary definitions

### `tools.ts` - Tool Function Interfaces
- `CanvasToolFunctions`: Interface for canvas manipulation functions
- `DrawingHistoryManager`: Interface for undo/redo functionality
- `AnimationToolFunctions`: Interface for animation calculations
- `FishAnimatorInterface`: Interface for fish animation controller
- `ToolEventHandlers`: Interface for mouse/touch event handling

### `store.ts` - Pinia Store Types
- `DrawingStoreState`, `DrawingStoreActions`: Drawing state management
- `FishStoreState`, `FishStoreActions`: Fish data management
- Store getters interfaces for computed properties

### `components.ts` - Vue Component Types
- Props and emits interfaces for all Vue components
- Type-safe component communication definitions
- Event payload type definitions

### `utils.ts` - Utility Types
- `ApiResponse`: Generic API response wrapper
- `FishApiInterface`: API endpoint definitions
- Constants for default values, storage keys, color palette
- Error types and performance metrics interfaces

## Requirements Addressed

This implementation addresses the following requirements:

- **Requirement 1.2**: Color setting functionality through `ToolSettings` interface
- **Requirement 2.5**: Current color and line thickness usage through drawing interfaces
- **Requirement 3.3**: Fish data saving through `FishData` interface with `imageData` and `name`

## Usage Examples

```typescript
import type { FishData, DrawingTool, ToolSettings } from '@/types'

// Create a fish
const fish: FishData = {
  id: 'fish-1',
  name: 'My Fish',
  imageData: canvasImageData,
  createdAt: new Date(),
  dimensions: { width: 100, height: 50 },
  position: { x: 0, y: 0 },
  velocity: { x: 2, y: 1 },
  animationState: { tailPhase: 0, direction: 1 }
}

// Configure drawing tool
const toolSettings: ToolSettings = {
  color: '#FF0000',
  size: 10,
  opacity: 1.0
}

// Set drawing tool
const currentTool: DrawingTool = 'brush'
```

## Testing

Type definitions are tested in `__tests__/types.test.ts` to ensure:
- All interfaces compile correctly
- Type constraints work as expected
- Default values are properly typed
- Complex nested types function correctly