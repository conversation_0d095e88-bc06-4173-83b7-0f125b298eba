import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import type { Router } from 'vue-router'
import { setActivePinia, createPinia } from 'pinia'
import { useFishStore } from '@/stores/fishStore'
import type { FishData } from '@/types'

// Mock the views to avoid component loading issues in tests
vi.mock('../views/DrawingPage.vue', () => ({
  default: { name: 'DrawingPage' }
}))

vi.mock('../views/SwimmingPage.vue', () => ({
  default: { name: 'SwimmingPage' }
}))

// Create a test router with the same configuration as the main router
function createTestRouter(): Router {
  return createRouter({
    history: createWebHistory(),
    routes: [
      {
        path: '/',
        name: 'Drawing',
        component: { name: 'DrawingPage' },
        meta: { 
          title: 'Draw a Fish!',
          description: '创造你独特的鱼类设计'
        }
      },
      {
        path: '/swimming/:fishId?',
        name: 'Swimming',
        component: { name: 'SwimmingPage' },
        meta: { 
          title: 'Watch Your Fish Swim!',
          description: '观看你的鱼在水中游泳',
          requiresFish: true
        },
        beforeEnter: (to, from, next) => {
          const fishStore = useFishStore()
          const fishId = to.params.fishId as string
          
          if (fishId && fishId !== '') {
            const fish = fishStore.fishCollection.find(f => f.id === fishId)
            if (fish) {
              fishStore.setCurrentFish(fish)
              next()
              return
            } else {
              next({ name: 'Drawing' })
              return
            }
          }
          
          if (!fishStore.hasFish) {
            next({ name: 'Drawing' })
            return
          }
          
          next()
        }
      },
      {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        redirect: { name: 'Drawing' }
      }
    ],
  })
}

describe('Router Configuration', () => {
  let router: Router
  let fishStore: ReturnType<typeof useFishStore>

  beforeEach(() => {
    setActivePinia(createPinia())
    router = createTestRouter()
    fishStore = useFishStore()
    // Clear any existing fish data
    fishStore.clearCollection()
    fishStore.setCurrentFish(null)
  })

  describe('Basic Route Navigation', () => {
    it('should navigate to drawing page by default', async () => {
      await router.push('/')
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })

    it('should redirect unknown routes to drawing page', async () => {
      await router.push('/unknown-route')
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })

    it('should have correct meta information for drawing page', async () => {
      await router.push('/')
      const route = router.currentRoute.value
      expect(route.meta.title).toBe('Draw a Fish!')
      expect(route.meta.description).toBe('创造你独特的鱼类设计')
    })

    it('should have correct meta information for swimming page', () => {
      const swimmingRoute = router.getRoutes().find(r => r.name === 'Swimming')
      expect(swimmingRoute?.meta.title).toBe('Watch Your Fish Swim!')
      expect(swimmingRoute?.meta.description).toBe('观看你的鱼在水中游泳')
      expect(swimmingRoute?.meta.requiresFish).toBe(true)
    })
  })

  describe('Swimming Page Route Guards', () => {
    const mockFish: FishData = {
      id: 'test-fish-123',
      name: 'Test Fish',
      imageData: 'mock-image-data',
      createdAt: new Date(),
      dimensions: { width: 400, height: 300 },
      position: { x: 100, y: 100 },
      velocity: { x: 2, y: 1 },
      animationState: { tailPhase: 0, direction: 1 }
    }

    it('should redirect to drawing page when no fish is available', async () => {
      // Ensure no fish is set
      fishStore.setCurrentFish(null)
      fishStore.clearCollection()
      
      await router.push('/swimming')
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })

    it('should allow access to swimming page when fish is available', async () => {
      // Set up a fish
      fishStore.setCurrentFish(mockFish)
      fishStore.addToCollection(mockFish)
      
      await router.push('/swimming')
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.path).toBe('/swimming')
    })

    it('should load specific fish by ID when provided', async () => {
      // Add fish to collection
      fishStore.addToCollection(mockFish)
      
      await router.push(`/swimming/${mockFish.id}`)
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.params.fishId).toBe(mockFish.id)
      expect(fishStore.currentFish?.id).toBe(mockFish.id)
    })

    it('should redirect to drawing page when fish ID is not found', async () => {
      // Clear collection
      fishStore.clearCollection()
      
      await router.push('/swimming/non-existent-fish-id')
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })

    it('should handle swimming page with optional fish ID parameter', async () => {
      const anotherFish: FishData = {
        ...mockFish,
        id: 'another-fish-456',
        name: 'Another Fish'
      }
      
      // Add multiple fish to collection
      fishStore.addToCollection(mockFish)
      fishStore.addToCollection(anotherFish)
      fishStore.setCurrentFish(mockFish)
      
      // Navigate to swimming page with specific fish ID
      await router.push(`/swimming/${anotherFish.id}`)
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(fishStore.currentFish?.id).toBe(anotherFish.id)
      expect(fishStore.currentFish?.name).toBe('Another Fish')
    })
  })

  describe('State Preservation During Navigation', () => {
    const mockFish: FishData = {
      id: 'state-test-fish',
      name: 'State Test Fish',
      imageData: 'mock-image-data',
      createdAt: new Date(),
      dimensions: { width: 400, height: 300 },
      position: { x: 150, y: 200 },
      velocity: { x: 1.5, y: 0.5 },
      animationState: { tailPhase: 1.5, direction: -1 }
    }

    it('should preserve fish state when navigating between pages', async () => {
      // Set up initial state
      fishStore.setCurrentFish(mockFish)
      fishStore.addToCollection(mockFish)
      
      // Navigate to swimming page
      await router.push('/swimming')
      expect(fishStore.currentFish?.id).toBe(mockFish.id)
      expect(fishStore.currentFish?.name).toBe(mockFish.name)
      
      // Navigate back to drawing page
      await router.push('/')
      expect(fishStore.currentFish?.id).toBe(mockFish.id)
      expect(fishStore.fishCollection).toHaveLength(1)
    })

    it('should maintain fish collection across navigation', async () => {
      const fish1: FishData = { ...mockFish, id: 'fish-1', name: 'Fish 1' }
      const fish2: FishData = { ...mockFish, id: 'fish-2', name: 'Fish 2' }
      
      fishStore.addToCollection(fish1)
      fishStore.addToCollection(fish2)
      fishStore.setCurrentFish(fish1)
      
      // Navigate to swimming page
      await router.push('/swimming')
      expect(fishStore.fishCollection).toHaveLength(2)
      expect(fishStore.currentFish?.id).toBe('fish-1')
      
      // Navigate to specific fish - manually set current fish to simulate route guard behavior
      fishStore.setCurrentFish(fish2)
      await router.push('/swimming/fish-2')
      expect(fishStore.fishCollection).toHaveLength(2)
      expect(fishStore.currentFish?.id).toBe('fish-2')
      
      // Navigate back to drawing
      await router.push('/')
      expect(fishStore.fishCollection).toHaveLength(2)
    })
  })

  describe('Route Parameters and Query Handling', () => {
    it('should handle optional fishId parameter correctly', async () => {
      const mockFish: FishData = {
        id: 'param-test-fish',
        name: 'Parameter Test Fish',
        imageData: 'mock-image-data',
        createdAt: new Date(),
        dimensions: { width: 400, height: 300 },
        position: { x: 100, y: 100 },
        velocity: { x: 2, y: 1 },
        animationState: { tailPhase: 0, direction: 1 }
      }
      
      fishStore.addToCollection(mockFish)
      fishStore.setCurrentFish(mockFish)
      
      // Test without fishId parameter - should be empty string, not undefined
      await router.push('/swimming')
      expect(router.currentRoute.value.params.fishId).toBe('')
      expect(router.currentRoute.value.name).toBe('Swimming')
      
      // Test with fishId parameter
      await router.push(`/swimming/${mockFish.id}`)
      expect(router.currentRoute.value.params.fishId).toBe(mockFish.id)
      expect(router.currentRoute.value.name).toBe('Swimming')
    })

    it('should handle route transitions correctly', async () => {
      const mockFish: FishData = {
        id: 'transition-test-fish',
        name: 'Transition Test Fish',
        imageData: 'mock-image-data',
        createdAt: new Date(),
        dimensions: { width: 400, height: 300 },
        position: { x: 100, y: 100 },
        velocity: { x: 2, y: 1 },
        animationState: { tailPhase: 0, direction: 1 }
      }
      
      fishStore.addToCollection(mockFish)
      fishStore.setCurrentFish(mockFish) // Need to set current fish for swimming page access
      
      // Start at drawing page
      await router.push('/')
      expect(router.currentRoute.value.name).toBe('Drawing')
      
      // Navigate to swimming page (should work now that we have a current fish)
      await router.push('/swimming')
      expect(router.currentRoute.value.name).toBe('Swimming')
      
      // Navigate back to drawing
      await router.push('/')
      expect(router.currentRoute.value.name).toBe('Drawing')
    })
  })

  describe('Error Handling', () => {
    it('should handle navigation errors gracefully', async () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      try {
        // This should not throw an error
        await router.push('/swimming/invalid-fish-id')
        expect(router.currentRoute.value.name).toBe('Drawing')
      } finally {
        consoleSpy.mockRestore()
      }
    })

    it('should redirect to drawing page for invalid routes', async () => {
      await router.push('/invalid/route/path')
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })
  })
})