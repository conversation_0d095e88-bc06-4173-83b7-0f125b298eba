// Core type definitions for DrawFish application

/**
 * Drawing tool types
 */
export type DrawingTool = 'brush' | 'eraser'

/**
 * Tool settings interface
 */
export interface ToolSettings {
  color: string
  size: number
  opacity: number
}

/**
 * Position interface for 2D coordinates
 */
export interface Position {
  x: number
  y: number
}

/**
 * Velocity interface for movement
 */
export interface Velocity {
  x: number
  y: number
}

/**
 * Dimensions interface
 */
export interface Dimensions {
  width: number
  height: number
}

/**
 * Animation state for fish swimming
 */
export interface AnimationState {
  tailPhase: number
  direction: number  // -1 for left, 1 for right
}

/**
 * Main fish data interface
 */
export interface FishData {
  id: string
  name: string
  imageData: ImageData | string  // Canvas ImageData or base64
  strokes?: FishStroke[]  // Vector representation of the drawing
  createdAt: Date
  dimensions: Dimensions
  position: Position
  velocity: Velocity
  animationState: AnimationState
}

/**
 * Drawing path point interface
 */
export interface DrawingPoint {
  x: number
  y: number
  pressure: number
}

/**
 * Drawing path interface
 */
export interface DrawingPath {
  points: DrawingPoint[]
  tool: DrawingTool
  settings: ToolSettings
  timestamp: number
}

/**
 * Fish stroke data for vector representation
 */
export interface FishStroke {
  id: string
  points: DrawingPoint[]
  color: string
  size: number
  tool: DrawingTool
  timestamp: number
}

/**
 * Canvas bounds interface
 */
export interface CanvasBounds {
  width: number
  height: number
  offsetX?: number
  offsetY?: number
}

// Re-export all types from other modules
export * from './tools'
export * from './store'
export * from './components'
export * from './utils'