import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { FishData, Position, Velocity, AnimationState, FishStroke } from '@/types'

export const useFishStore = defineStore('fish', () => {
  // State
  const currentFish = ref<FishData | null>(null)
  const fishCollection = ref<FishData[]>([])
  const isSwimming = ref(false)
  const swimBounds = ref({
    width: 800,
    height: 600
  })

  // Getters
  const hasFish = computed(() => currentFish.value !== null)
  const fishCount = computed(() => fishCollection.value.length)
  const getCurrentFishName = computed(() => currentFish.value?.name || null)

  // Actions
  function createFish(imageData: ImageData | string, name: string, strokes?: FishStroke[]): FishData {
    const trimmedName = name.trim()
    const fishWidth = imageData instanceof ImageData ? imageData.width : 400
    const fishHeight = imageData instanceof ImageData ? imageData.height : 300
    
    const fish: FishData = {
      id: `fish_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: trimmedName || 'Unnamed Fish',
      imageData,
      strokes: strokes || [],
      createdAt: new Date(),
      dimensions: {
        width: fishWidth,
        height: fishHeight
      },
      position: {
        // Start from right side of the screen
        x: swimBounds.value.width - fishWidth - 50,
        // Center vertically with some random offset
        y: (swimBounds.value.height - fishHeight) / 2 + (Math.random() - 0.5) * 100
      },
      velocity: {
        // Start swimming left (negative x velocity) - increased speed for visibility
        x: -3,
        y: (Math.random() - 0.5) * 1 // Slightly more vertical movement
      },
      animationState: {
        tailPhase: 0,
        direction: -1 // Start facing left
      }
    }
    
    setCurrentFish(fish)
    addToCollection(fish)
    return fish
  }

  function setCurrentFish(fish: FishData | null) {
    currentFish.value = fish
  }

  function addToCollection(fish: FishData) {
    const existingIndex = fishCollection.value.findIndex(f => f.id === fish.id)
    if (existingIndex >= 0) {
      fishCollection.value[existingIndex] = fish
    } else {
      fishCollection.value.push(fish)
    }
    
    // Persist to localStorage
    persistFishCollection()
  }

  function removeFromCollection(fishId: string) {
    const index = fishCollection.value.findIndex(f => f.id === fishId)
    if (index >= 0) {
      fishCollection.value.splice(index, 1)
      
      // If removed fish was current fish, clear current fish
      if (currentFish.value?.id === fishId) {
        currentFish.value = null
      }
      
      persistFishCollection()
    }
  }

  function startSwimming() {
    isSwimming.value = true
  }

  function stopSwimming() {
    isSwimming.value = false
  }

  // Persistence helpers
  function persistFishCollection() {
    try {
      const serializedFish = fishCollection.value.map(fish => ({
        ...fish,
        imageData: typeof fish.imageData === 'string' ? fish.imageData : 'ImageData_placeholder'
      }))
      localStorage.setItem('drawfish_collection', JSON.stringify(serializedFish))
    } catch (error) {
      console.warn('Failed to persist fish collection:', error)
    }
  }

  function loadFishCollection() {
    try {
      const stored = localStorage.getItem('drawfish_collection')
      if (stored) {
        const parsed = JSON.parse(stored)
        fishCollection.value = parsed
      }
    } catch (error) {
      console.warn('Failed to load fish collection:', error)
    }
  }

  function updateFishPosition(fishId: string, position: Position) {
    const fish = fishCollection.value.find(f => f.id === fishId)
    if (fish) {
      fish.position = { ...position }
      
      // Update current fish if it's the same
      if (currentFish.value?.id === fishId) {
        currentFish.value.position = { ...position }
      }
    }
  }

  function updateFishVelocity(fishId: string, velocity: Velocity) {
    const fish = fishCollection.value.find(f => f.id === fishId)
    if (fish) {
      fish.velocity = { ...velocity }
      
      // Update current fish if it's the same
      if (currentFish.value?.id === fishId) {
        currentFish.value.velocity = { ...velocity }
      }
    }
  }

  function updateAnimationState(fishId: string, animationState: Partial<AnimationState>) {
    const fish = fishCollection.value.find(f => f.id === fishId)
    if (fish) {
      fish.animationState = { ...fish.animationState, ...animationState }
      
      // Update current fish if it's the same
      if (currentFish.value?.id === fishId) {
        currentFish.value.animationState = { ...fish.animationState }
      }
    }
  }

  function setSwimBounds(bounds: { width: number; height: number }) {
    swimBounds.value = { ...bounds }
  }

  function clearCollection() {
    fishCollection.value = []
    currentFish.value = null
    persistFishCollection()
  }

  // Initialize store
  loadFishCollection()

  return {
    // State
    currentFish,
    fishCollection,
    isSwimming,
    swimBounds,
    
    // Getters
    hasFish,
    fishCount,
    getCurrentFishName,
    
    // Actions
    createFish,
    setCurrentFish,
    addToCollection,
    removeFromCollection,
    startSwimming,
    stopSwimming,
    updateFishPosition,
    updateFishVelocity,
    updateAnimationState,
    setSwimBounds,
    clearCollection
  }
})