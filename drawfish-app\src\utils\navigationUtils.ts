import type { Router } from 'vue-router'
import { useFishStore } from '@/stores/fishStore'
import type { FishData } from '@/types'

/**
 * Navigation utilities for the DrawFish application
 */

/**
 * Navigate to the drawing page
 */
export function navigateToDrawing(router: Router): Promise<void> {
  return router.push({ name: 'Drawing' })
}

/**
 * Navigate to the swimming page with optional fish ID
 */
export function navigateToSwimming(router: Router, fishId?: string): Promise<void> {
  if (fishId) {
    return router.push({ name: 'Swimming', params: { fishId } })
  }
  return router.push({ name: 'Swimming' })
}

/**
 * Navigate to swimming page with a specific fish
 */
export function navigateToSwimmingWithFish(router: Router, fish: FishData): Promise<void> {
  const fishStore = useFishStore()
  
  // Ensure the fish is in the collection and set as current
  fishStore.addToCollection(fish)
  fishStore.setCurrentFish(fish)
  
  return navigateToSwimming(router, fish.id)
}

/**
 * Check if navigation to swimming page is allowed
 */
export function canNavigateToSwimming(fishId?: string): boolean {
  const fishStore = useFishStore()
  
  if (fishId) {
    // Check if specific fish exists
    return fishStore.fishCollection.some(f => f.id === fishId)
  }
  
  // Check if any fish is available
  return fishStore.hasFish
}

/**
 * Get the current route name safely
 */
export function getCurrentRouteName(router: Router): string | null {
  return router.currentRoute.value.name as string | null
}

/**
 * Check if currently on a specific route
 */
export function isCurrentRoute(router: Router, routeName: string): boolean {
  return getCurrentRouteName(router) === routeName
}

/**
 * Navigate back to the previous page or fallback to drawing page
 */
export function navigateBack(router: Router): Promise<void> {
  // Check if there's history to go back to
  if (window.history.length > 1) {
    router.back()
    return Promise.resolve()
  } else {
    // Fallback to drawing page
    return navigateToDrawing(router)
  }
}

/**
 * Create a fish and navigate to swimming page
 */
export async function createFishAndNavigate(
  router: Router, 
  imageData: ImageData | string, 
  name: string
): Promise<FishData> {
  const fishStore = useFishStore()
  
  try {
    const fish = fishStore.createFish(imageData, name)
    await navigateToSwimmingWithFish(router, fish)
    return fish
  } catch (error) {
    console.error('Failed to create fish and navigate:', error)
    throw error
  }
}

/**
 * Navigation guard helper - check if user can access swimming page
 */
export function validateSwimmingPageAccess(fishId?: string): {
  canAccess: boolean
  redirectTo?: string
  reason?: string
} {
  const fishStore = useFishStore()
  
  if (fishId && fishId !== '') {
    const fish = fishStore.fishCollection.find(f => f.id === fishId)
    if (!fish) {
      return {
        canAccess: false,
        redirectTo: 'Drawing',
        reason: `Fish with ID ${fishId} not found`
      }
    }
    return { canAccess: true }
  }
  
  if (!fishStore.hasFish) {
    return {
      canAccess: false,
      redirectTo: 'Drawing',
      reason: 'No fish available for swimming page'
    }
  }
  
  return { canAccess: true }
}

/**
 * Get navigation breadcrumbs for current route
 */
export function getNavigationBreadcrumbs(router: Router): Array<{
  name: string
  title: string
  path: string
  current: boolean
}> {
  const currentRoute = router.currentRoute.value
  const breadcrumbs = []
  
  // Always include drawing page
  breadcrumbs.push({
    name: 'Drawing',
    title: '绘画页面',
    path: '/',
    current: currentRoute.name === 'Drawing'
  })
  
  // Add swimming page if current
  if (currentRoute.name === 'Swimming') {
    const fishStore = useFishStore()
    const fishName = fishStore.currentFish?.name || '游泳页面'
    
    breadcrumbs.push({
      name: 'Swimming',
      title: fishName,
      path: currentRoute.path,
      current: true
    })
  }
  
  return breadcrumbs
}

/**
 * Navigation state manager
 */
export class NavigationState {
  private router: Router
  private navigationHistory: string[] = []
  
  constructor(router: Router) {
    this.router = router
    this.setupNavigationTracking()
  }
  
  private setupNavigationTracking(): void {
    this.router.afterEach((to) => {
      this.navigationHistory.push(to.path)
      
      // Keep only last 10 navigation entries
      if (this.navigationHistory.length > 10) {
        this.navigationHistory.shift()
      }
    })
  }
  
  getNavigationHistory(): string[] {
    return [...this.navigationHistory]
  }
  
  getPreviousRoute(): string | null {
    return this.navigationHistory.length > 1 
      ? this.navigationHistory[this.navigationHistory.length - 2]
      : null
  }
  
  clearHistory(): void {
    this.navigationHistory = []
  }
}

/**
 * Route transition helpers
 */
export const routeTransitions = {
  /**
   * Fade transition for route changes
   */
  fade: {
    name: 'fade',
    mode: 'out-in' as const
  },
  
  /**
   * Slide transition for route changes
   */
  slide: {
    name: 'slide',
    mode: 'out-in' as const
  }
}

/**
 * URL utilities
 */
export function generateSwimmingUrl(fishId: string): string {
  return `/swimming/${fishId}`
}

export function generateDrawingUrl(): string {
  return '/'
}

/**
 * Route meta helpers
 */
export function getRouteTitle(router: Router): string {
  const route = router.currentRoute.value
  return route.meta.title as string || 'DrawFish'
}

export function getRouteDescription(router: Router): string {
  const route = router.currentRoute.value
  return route.meta.description as string || ''
}