import { createRouter, createWebHistory } from 'vue-router'
import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import DrawingPage from '../views/DrawingPage.vue'
import { useFishStore } from '@/stores/fishStore'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Drawing',
      component: DrawingPage,
      meta: { 
        title: 'Draw a Fish!',
        description: '创造你独特的鱼类设计'
      }
    },
    {
      path: '/swimming/:fishId?',
      name: 'Swimming',
      component: () => import('../views/SwimmingPage.vue'),
      meta: { 
        title: 'Watch Your Fish Swim!',
        description: '观看你的鱼在水中游泳',
        requiresFish: true
      },
      beforeEnter: (
        to: RouteLocationNormalized, 
        from: RouteLocationNormalized, 
        next: NavigationGuardNext
      ) => {
        const fishStore = useFishStore()
        const fishId = to.params.fishId as string
        
        // 如果有指定的鱼ID，尝试从集合中找到该鱼
        if (fishId && fishId !== '') {
          const fish = fishStore.fishCollection.find(f => f.id === fishId)
          if (fish) {
            fishStore.setCurrentFish(fish)
            next()
            return
          } else {
            console.warn(`Fish with ID ${fishId} not found`)
            // 鱼不存在，重定向到绘画页面
            next({ name: 'Drawing' })
            return
          }
        }
        
        // 如果没有指定鱼ID，检查是否有当前鱼
        if (!fishStore.hasFish) {
          console.warn('No fish available for swimming page')
          // 没有鱼类数据，重定向到绘画页面
          next({ name: 'Drawing' })
          return
        }
        
        // 有鱼类数据，允许访问
        next()
      }
    },
    {
      // 捕获所有未匹配的路由
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: { name: 'Drawing' }
    }
  ],
})

// 全局前置守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - DrawFish`
  } else {
    document.title = 'DrawFish - 绘制你的鱼'
  }
  
  next()
})

// 全局后置钩子 - 页面切换后的处理
router.afterEach((to, from) => {
  // 记录页面访问
  console.log(`Navigated from ${from.name || 'unknown'} to ${to.name || 'unknown'}`)
  
  // 可以在这里添加页面访问统计或其他后处理逻辑
})

// 导航错误处理
router.onError((error) => {
  console.error('Router navigation error:', error)
  
  // 导航错误时重定向到首页
  router.push({ name: 'Drawing' })
})

export default router
