import './setup'
import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createP<PERSON> } from 'pinia'
import { useDrawingStore } from '../drawingStore'
import type { DrawingTool } from '@/types'

describe('Drawing Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('初始状态', () => {
    it('应该有正确的初始状态', () => {
      const store = useDrawingStore()
      
      expect(store.currentTool).toBe('brush')
      expect(store.currentColor).toBe('#000000')
      expect(store.brushSize).toBe(5)
      expect(store.canvasHistory).toEqual([])
      expect(store.historyIndex).toBe(-1)
      expect(store.isDrawing).toBe(false)
      expect(store.lastDrawnFish).toBe(null)
    })

    it('应该有正确的工具设置', () => {
      const store = useDrawingStore()
      
      expect(store.toolSettings.color).toBe('#000000')
      expect(store.toolSettings.size).toBe(5)
      expect(store.toolSettings.opacity).toBe(1)
    })
  })

  describe('工具切换', () => {
    it('应该能切换绘画工具', () => {
      const store = useDrawingStore()
      
      store.setTool('eraser')
      expect(store.currentTool).toBe('eraser')
      
      store.setTool('brush')
      expect(store.currentTool).toBe('brush')
    })

    it('橡皮擦工具应该使用白色', () => {
      const store = useDrawingStore()
      
      store.setTool('eraser')
      expect(store.toolSettings.color).toBe('#FFFFFF')
    })
  })

  describe('颜色设置', () => {
    it('应该能设置画笔颜色', () => {
      const store = useDrawingStore()
      
      store.setColor('#FF0000')
      expect(store.currentColor).toBe('#FF0000')
      expect(store.toolSettings.color).toBe('#FF0000')
    })

    it('橡皮擦模式下不应该改变颜色', () => {
      const store = useDrawingStore()
      
      store.setTool('eraser')
      store.setColor('#FF0000')
      
      expect(store.currentColor).toBe('#000000') // 保持原色
      expect(store.toolSettings.color).toBe('#FFFFFF') // 橡皮擦始终白色
    })
  })

  describe('画笔大小', () => {
    it('应该能设置画笔大小', () => {
      const store = useDrawingStore()
      
      store.setBrushSize(10)
      expect(store.brushSize).toBe(10)
      expect(store.toolSettings.size).toBe(10)
    })

    it('应该限制画笔大小范围', () => {
      const store = useDrawingStore()
      
      store.setBrushSize(0) // 小于最小值
      expect(store.brushSize).toBe(5) // 保持原值
      
      store.setBrushSize(100) // 大于最大值
      expect(store.brushSize).toBe(5) // 保持原值
      
      store.setBrushSize(25) // 有效范围内
      expect(store.brushSize).toBe(25)
    })
  })

  describe('工具设置', () => {
    it('应该能批量设置工具参数', () => {
      const store = useDrawingStore()
      
      store.setToolSettings({
        color: '#00FF00',
        size: 15
      })
      
      expect(store.currentColor).toBe('#00FF00')
      expect(store.brushSize).toBe(15)
    })

    it('橡皮擦模式下不应该改变颜色设置', () => {
      const store = useDrawingStore()
      
      store.setTool('eraser')
      store.setToolSettings({
        color: '#00FF00',
        size: 15
      })
      
      expect(store.currentColor).toBe('#000000') // 颜色不变
      expect(store.brushSize).toBe(15) // 大小改变
    })
  })

  describe('历史记录管理', () => {
    it('应该能添加历史记录', () => {
      const store = useDrawingStore()
      const mockImageData = new ImageData(100, 100)
      
      store.addToHistory(mockImageData)
      
      expect(store.canvasHistory).toHaveLength(1)
      expect(store.historyIndex).toBe(0)
      expect(store.canUndo).toBe(false) // 只有一个状态时不能撤销
      expect(store.canRedo).toBe(false)
    })

    it('应该能撤销操作', () => {
      const store = useDrawingStore()
      const mockImageData1 = new ImageData(100, 100)
      const mockImageData2 = new ImageData(100, 100)
      
      store.addToHistory(mockImageData1)
      store.addToHistory(mockImageData2)
      
      expect(store.canUndo).toBe(true)
      
      const undoResult = store.undo()
      expect(undoResult).toStrictEqual(mockImageData1)
      expect(store.historyIndex).toBe(0)
      expect(store.canRedo).toBe(true)
    })

    it('应该能重做操作', () => {
      const store = useDrawingStore()
      const mockImageData1 = new ImageData(100, 100)
      const mockImageData2 = new ImageData(100, 100)
      
      store.addToHistory(mockImageData1)
      store.addToHistory(mockImageData2)
      store.undo()
      
      const redoResult = store.redo()
      expect(redoResult).toStrictEqual(mockImageData2)
      expect(store.historyIndex).toBe(1)
      expect(store.canRedo).toBe(false)
    })

    it('添加新历史时应该清除重做历史', () => {
      const store = useDrawingStore()
      const mockImageData1 = new ImageData(100, 100)
      const mockImageData2 = new ImageData(100, 100)
      const mockImageData3 = new ImageData(100, 100)
      
      store.addToHistory(mockImageData1)
      store.addToHistory(mockImageData2)
      store.undo() // 回到第一个状态
      
      expect(store.canRedo).toBe(true)
      
      store.addToHistory(mockImageData3) // 添加新状态
      
      expect(store.canRedo).toBe(false) // 重做历史被清除
      expect(store.canvasHistory).toHaveLength(2) // 只有两个状态
    })

    it('应该限制历史记录数量', () => {
      const store = useDrawingStore()
      
      // 添加超过限制的历史记录
      for (let i = 0; i < 55; i++) {
        store.addToHistory(new ImageData(100, 100))
      }
      
      expect(store.canvasHistory.length).toBeLessThanOrEqual(50)
    })
  })

  describe('画布操作', () => {
    it('应该能清空画布', () => {
      const store = useDrawingStore()
      
      store.addToHistory(new ImageData(100, 100))
      store.clearCanvas()
      
      expect(store.canvasHistory).toEqual([])
      expect(store.historyIndex).toBe(-1)
      expect(store.hasContent).toBe(false)
    })

    it('应该能设置绘画状态', () => {
      const store = useDrawingStore()
      
      store.setDrawingState(true)
      expect(store.isDrawing).toBe(true)
      
      store.setDrawingState(false)
      expect(store.isDrawing).toBe(false)
    })
  })

  describe('鱼类数据管理', () => {
    it('应该能设置最后绘制的鱼', () => {
      const store = useDrawingStore()
      const mockFish = {
        id: 'test-fish',
        name: 'Test Fish',
        imageData: new ImageData(100, 100),
        createdAt: new Date(),
        dimensions: { width: 100, height: 100 },
        position: { x: 0, y: 0 },
        velocity: { x: 1, y: 1 },
        animationState: { tailPhase: 0, direction: 1 }
      }
      
      store.setLastDrawnFish(mockFish)
      expect(store.lastDrawnFish).toStrictEqual(mockFish)
      
      store.setLastDrawnFish(null)
      expect(store.lastDrawnFish).toBe(null)
    })
  })

  describe('状态重置', () => {
    it('应该能重置所有绘画状态', () => {
      const store = useDrawingStore()
      
      // 修改所有状态
      store.setTool('eraser')
      store.setColor('#FF0000')
      store.setBrushSize(20)
      store.addToHistory(new ImageData(100, 100))
      store.setDrawingState(true)
      store.setLastDrawnFish({
        id: 'test',
        name: 'test',
        imageData: new ImageData(100, 100),
        createdAt: new Date(),
        dimensions: { width: 100, height: 100 },
        position: { x: 0, y: 0 },
        velocity: { x: 1, y: 1 },
        animationState: { tailPhase: 0, direction: 1 }
      })
      
      store.resetDrawingState()
      
      // 验证所有状态都被重置
      expect(store.currentTool).toBe('brush')
      expect(store.currentColor).toBe('#000000')
      expect(store.brushSize).toBe(5)
      expect(store.canvasHistory).toEqual([])
      expect(store.historyIndex).toBe(-1)
      expect(store.isDrawing).toBe(false)
      expect(store.lastDrawnFish).toBe(null)
    })
  })
})