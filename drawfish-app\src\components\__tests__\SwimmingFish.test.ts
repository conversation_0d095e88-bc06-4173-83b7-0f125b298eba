import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import SwimmingFish from '../SwimmingFish.vue'
import type { FishData, CanvasBounds } from '../../types'

// Mock the FishAnimator
const mockAnimator = {
  startAnimation: vi.fn(),
  stopAnimation: vi.fn(),
  pause: vi.fn(),
  resume: vi.fn(),
  updateBounds: vi.fn(),
  getTailOffset: vi.fn(() => 5),
  reset: vi.fn(),
  destroy: vi.fn()
}

vi.mock('../../utils/fishAnimator', () => ({
  FishAnimator: vi.fn(() => mockAnimator)
}))

describe('SwimmingFish', () => {
  let mockFishData: FishData
  let mockBounds: CanvasBounds
  
  beforeEach(() => {
    vi.clearAllMocks()
    
    mockFishData = {
      id: 'test-fish',
      name: '<PERSON> Fish',
      imageData: 'data:image/png;base64,mock-image-data',
      createdAt: new Date(),
      dimensions: { width: 50, height: 30 },
      position: { x: 100, y: 100 },
      velocity: { x: 2, y: 1 },
      animationState: {
        tailPhase: 0,
        direction: 1
      }
    }
    
    mockBounds = {
      width: 800,
      height: 600,
      top: 0,
      left: 0
    }
    
    // Mock canvas context
    const mockContext = {
      clearRect: vi.fn(),
      drawImage: vi.fn(),
      putImageData: vi.fn(),
      fillStyle: '',
      beginPath: vi.fn(),
      ellipse: vi.fn(),
      fill: vi.fn(),
      moveTo: vi.fn(),
      lineTo: vi.fn(),
      closePath: vi.fn(),
      arc: vi.fn()
    }
    
    HTMLCanvasElement.prototype.getContext = vi.fn(() => mockContext)
    
    // Mock Image
    global.Image = class {
      onload: (() => void) | null = null
      src = ''
      
      constructor() {
        setTimeout(() => {
          if (this.onload) {
            this.onload()
          }
        }, 0)
      }
    } as any
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('component rendering', () => {
    it('should render fish component with canvas', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      expect(wrapper.find('.swimming-fish').exists()).toBe(true)
      expect(wrapper.find('.fish-canvas').exists()).toBe(true)
    })
    
    it('should render fish name when showName is true', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          showName: true
        }
      })
      
      const nameElement = wrapper.find('.fish-name')
      expect(nameElement.exists()).toBe(true)
      expect(nameElement.text()).toBe('Test Fish')
    })
    
    it('should not render fish name when showName is false', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          showName: false
        }
      })
      
      expect(wrapper.find('.fish-name').exists()).toBe(false)
    })
    
    it('should apply correct positioning styles', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      const fishElement = wrapper.find('.swimming-fish')
      const style = fishElement.attributes('style')
      
      expect(style).toContain('left: 100px')
      expect(style).toContain('top: 100px')
      expect(style).toContain('position: absolute')
    })
  })
  
  describe('animation control', () => {
    it('should start animation automatically when autoStart is true', async () => {
      mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          autoStart: true
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(mockAnimator.startAnimation).toHaveBeenCalled()
    })
    
    it('should not start animation automatically when autoStart is false', async () => {
      mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          autoStart: false
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      expect(mockAnimator.startAnimation).not.toHaveBeenCalled()
    })
    
    it('should expose animation control methods', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          autoStart: false
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      const vm = wrapper.vm as any
      
      vm.startAnimation()
      expect(mockAnimator.startAnimation).toHaveBeenCalled()
      
      vm.stopAnimation()
      expect(mockAnimator.stopAnimation).toHaveBeenCalled()
      
      vm.pauseAnimation()
      expect(mockAnimator.pause).toHaveBeenCalled()
      
      vm.resumeAnimation()
      expect(mockAnimator.resume).toHaveBeenCalled()
    })
  })
  
  describe('interactivity', () => {
    it('should emit fishClick event when interactive and clicked', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          interactive: true
        }
      })
      
      await wrapper.find('.swimming-fish').trigger('click')
      
      expect(wrapper.emitted('fishClick')).toBeTruthy()
      expect(wrapper.emitted('fishClick')?.[0]).toEqual([mockFishData])
    })
    
    it('should not emit fishClick event when not interactive', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          interactive: false
        }
      })
      
      await wrapper.find('.swimming-fish').trigger('click')
      
      expect(wrapper.emitted('fishClick')).toBeFalsy()
    })
    
    it('should apply pointer cursor when interactive', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          interactive: true
        }
      })
      
      const style = wrapper.find('.swimming-fish').attributes('style')
      expect(style).toContain('cursor: pointer')
    })
    
    it('should apply default cursor when not interactive', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          interactive: false
        }
      })
      
      const style = wrapper.find('.swimming-fish').attributes('style')
      expect(style).toContain('cursor: default')
    })
  })
  
  describe('canvas initialization', () => {
    it('should set canvas dimensions from fish data', () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      const canvas = wrapper.find('.fish-canvas')
      expect(canvas.attributes('width')).toBe('50')
      expect(canvas.attributes('height')).toBe('30')
    })
    
    it('should handle base64 image data', async () => {
      const fishWithBase64 = {
        ...mockFishData,
        imageData: 'data:image/png;base64,mock-base64-data'
      }
      
      // Spy on Image constructor
      const ImageSpy = vi.fn().mockImplementation(() => ({
        onload: null,
        src: '',
        addEventListener: vi.fn()
      }))
      global.Image = ImageSpy
      
      mount(SwimmingFish, {
        props: {
          fishData: fishWithBase64,
          bounds: mockBounds
        }
      })
      
      // Wait for image loading
      await new Promise(resolve => setTimeout(resolve, 10))
      
      // Should create Image object
      expect(ImageSpy).toHaveBeenCalled()
    })
  })
  
  describe('bounds updates', () => {
    it('should update animator bounds when bounds prop changes', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      const newBounds = {
        width: 1000,
        height: 800,
        top: 0,
        left: 0
      }
      
      await wrapper.setProps({ bounds: newBounds })
      
      expect(mockAnimator.updateBounds).toHaveBeenCalledWith(newBounds)
    })
  })
  
  describe('fish data updates', () => {
    it('should reset animator when fish data changes', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      const newFishData = {
        ...mockFishData,
        name: 'Updated Fish',
        position: { x: 200, y: 150 }
      }
      
      await wrapper.setProps({ fishData: newFishData })
      
      expect(mockAnimator.reset).toHaveBeenCalledWith(newFishData)
    })
  })
  
  describe('component cleanup', () => {
    it('should destroy animator on unmount', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      wrapper.unmount()
      
      expect(mockAnimator.destroy).toHaveBeenCalled()
    })
  })
  
  describe('events', () => {
    it('should emit animationStart when animation starts', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          autoStart: false
        }
      })
      
      // Wait for component to mount
      await new Promise(resolve => setTimeout(resolve, 0))
      
      const vm = wrapper.vm as any
      vm.startAnimation()
      
      expect(wrapper.emitted('animationStart')).toBeTruthy()
    })
    
    it('should emit animationStop when animation stops', async () => {
      const wrapper = mount(SwimmingFish, {
        props: {
          fishData: mockFishData,
          bounds: mockBounds,
          autoStart: true
        }
      })
      
      // Wait for component to mount and auto-start
      await new Promise(resolve => setTimeout(resolve, 0))
      
      const vm = wrapper.vm as any
      vm.stopAnimation()
      
      expect(wrapper.emitted('animationStop')).toBeTruthy()
    })
  })
})