import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import DrawingCanvas from '../DrawingCanvas.vue'
import { useDrawingStore } from '@/stores/drawingStore'
import { CanvasUtils } from '@/utils/canvasUtils'

// Mock CanvasUtils
vi.mock('@/utils/canvasUtils', () => ({
    CanvasUtils: {
        initializeCanvas: vi.fn(),
        startDrawing: vi.fn(),
        continueDrawing: vi.fn(),
        endDrawing: vi.fn(),
        eraseAt: vi.fn(),
        eraseAlongPath: vi.fn(),
        clearCanvas: vi.fn(),
        flipCanvas: vi.fn(),
        getImageData: vi.fn(),
        setImageData: vi.fn(),
        getCanvasBounds: vi.fn(),
        screenToCanvasCoordinates: vi.fn()
    },
    DrawingHistoryManager: vi.fn().mockImplementation(() => ({
        addToHistory: vi.fn(),
        undo: vi.fn(),
        redo: vi.fn(),
        canUndo: vi.fn(() => false),
        canRedo: vi.fn(() => false),
        clearHistory: vi.fn()
    })),
    DrawingPathRecorder: vi.fn().mockImplementation(() => ({
        startPath: vi.fn(),
        addPoint: vi.fn(),
        endPath: vi.fn(() => []),
        getCurrentPath: vi.fn(() => []),
        isCurrentlyRecording: vi.fn(() => false),
        clearCurrentPath: vi.fn()
    }))
}))

// Mock ImageData for testing environment
class MockImageData {
    width: number
    height: number
    data: Uint8ClampedArray

    constructor(width: number, height: number) {
        this.width = width
        this.height = height
        this.data = new Uint8ClampedArray(width * height * 4)
    }
}

global.ImageData = MockImageData as any

describe('DrawingCanvas - Eraser Integration', () => {
    let drawingStore: ReturnType<typeof useDrawingStore>
    let mockContext: any

    beforeEach(() => {
        // Setup Pinia
        const pinia = createPinia()
        setActivePinia(pinia)
        drawingStore = useDrawingStore()

        // Mock canvas context
        mockContext = {
            lineCap: 'round',
            lineJoin: 'round',
            imageSmoothingEnabled: true,
            globalCompositeOperation: 'source-over'
        }

            // Setup CanvasUtils mocks
            ; (CanvasUtils.initializeCanvas as MockedFunction<any>).mockReturnValue(mockContext)
            ; (CanvasUtils.getCanvasBounds as MockedFunction<any>).mockReturnValue({
                width: 800,
                height: 600,
                top: 0,
                left: 0
            })
            ; (CanvasUtils.screenToCanvasCoordinates as MockedFunction<any>).mockReturnValue({
                x: 100,
                y: 100
            })
            ; (CanvasUtils.getImageData as MockedFunction<any>).mockReturnValue(new MockImageData(800, 600))

        vi.clearAllMocks()
    })

    describe('Complete Eraser Workflow', () => {
        it('should handle complete eraser workflow from tool selection to erasing', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            // Wait for component to mount
            await wrapper.vm.$nextTick()

            // 1. Switch to eraser tool
            drawingStore.setTool('eraser')
            expect(drawingStore.currentTool).toBe('eraser')

            // 2. Set eraser size
            drawingStore.setBrushSize(20)
            expect(drawingStore.brushSize).toBe(20)

            // 3. Verify tool settings for eraser
            const settings = drawingStore.currentToolSettings
            expect(settings.color).toBe('#FFFFFF')
            expect(settings.size).toBe(20)
            expect(settings.opacity).toBe(1)

            // 4. Simulate mouse down event (start erasing)
            const canvas = wrapper.find('canvas')
            await canvas.trigger('mousedown', {
                clientX: 150,
                clientY: 200
            })

            // Should start drawing state
            expect(drawingStore.isDrawing).toBe(true)

            // Should call eraseAt method
            expect(CanvasUtils.eraseAt).toHaveBeenCalledWith(
                mockContext,
                { x: 100, y: 100, pressure: 1 },
                20
            )

            // 5. Simulate mouse move event (continue erasing)
            await canvas.trigger('mousemove', {
                clientX: 160,
                clientY: 210
            })

            // Should call eraseAt for continuous erasing (at least once more)
            expect(CanvasUtils.eraseAt).toHaveBeenCalledTimes(1)

            // 6. Simulate mouse up event (end erasing)
            await canvas.trigger('mouseup', {
                clientX: 160,
                clientY: 210
            })

            // Should end drawing state
            expect(drawingStore.isDrawing).toBe(false)

            // Should save to history
            expect(CanvasUtils.getImageData).toHaveBeenCalled()
        })

        it('should handle tool switching between brush and eraser', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            // Start with brush
            drawingStore.setTool('brush')
            drawingStore.setColor('#FF0000')
            expect(drawingStore.currentToolSettings.color).toBe('#FF0000')

            // Switch to eraser
            drawingStore.setTool('eraser')
            expect(drawingStore.currentToolSettings.color).toBe('#FFFFFF')

            // Switch back to brush
            drawingStore.setTool('brush')
            expect(drawingStore.currentToolSettings.color).toBe('#FF0000')
        })

        it('should apply correct cursor style for eraser tool', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            // Switch to eraser tool
            drawingStore.setTool('eraser')
            await wrapper.vm.$nextTick()

            // Check that data-tool attribute is set
            const canvas = wrapper.find('canvas')
            expect(canvas.attributes('data-tool')).toBe('eraser')

            // Switch to brush tool
            drawingStore.setTool('brush')
            await wrapper.vm.$nextTick()

            expect(canvas.attributes('data-tool')).toBe('brush')
        })

        it('should handle touch events for eraser tool', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            // Switch to eraser tool
            drawingStore.setTool('eraser')

            // Simulate touch start (simplified for testing environment)
            const canvas = wrapper.find('canvas')
            await canvas.trigger('touchstart', {
                touches: [{
                    clientX: 150,
                    clientY: 200
                }]
            })

            expect(drawingStore.isDrawing).toBe(true)
            expect(CanvasUtils.eraseAt).toHaveBeenCalled()

            // Simulate touch end
            await canvas.trigger('touchend', {
                changedTouches: [{
                    clientX: 150,
                    clientY: 200
                }]
            })

            expect(drawingStore.isDrawing).toBe(false)
        })

        it('should handle eraser size changes correctly', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            // Switch to eraser and test different sizes
            drawingStore.setTool('eraser')

            const sizes = [5, 10, 15, 25, 30]

            for (const size of sizes) {
                drawingStore.setBrushSize(size)
                expect(drawingStore.currentToolSettings.size).toBe(size)

                // Simulate erasing with this size
                const canvas = wrapper.find('canvas')
                await canvas.trigger('mousedown', {
                    clientX: 100,
                    clientY: 100
                })

                expect(CanvasUtils.eraseAt).toHaveBeenCalledWith(
                    mockContext,
                    { x: 100, y: 100, pressure: 1 },
                    size
                )

                await canvas.trigger('mouseup')
                vi.clearAllMocks()
            }
        })

        it('should maintain eraser functionality after undo/redo operations', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            // Switch to eraser
            drawingStore.setTool('eraser')

            // Perform erasing operation
            const canvas = wrapper.find('canvas')
            await canvas.trigger('mousedown', { clientX: 100, clientY: 100 })
            await canvas.trigger('mouseup')

            // Tool should still be eraser after operation
            expect(drawingStore.currentTool).toBe('eraser')
            expect(drawingStore.currentToolSettings.color).toBe('#FFFFFF')

            // Should be able to continue erasing
            await canvas.trigger('mousedown', { clientX: 150, clientY: 150 })
            expect(CanvasUtils.eraseAt).toHaveBeenCalled()
        })
    })

    describe('Eraser Error Handling', () => {
        it('should handle eraser operations when canvas context is null', async () => {
            ; (CanvasUtils.initializeCanvas as MockedFunction<any>).mockReturnValue(null)

            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            drawingStore.setTool('eraser')

            // Should not throw error even with null context
            const canvas = wrapper.find('canvas')
            await expect(canvas.trigger('mousedown', { clientX: 100, clientY: 100 })).resolves.not.toThrow()
        })

        it('should handle invalid coordinates gracefully', async () => {
            const wrapper = mount(DrawingCanvas, {
                props: {
                    width: 800,
                    height: 600
                }
            })

            await wrapper.vm.$nextTick()

            drawingStore.setTool('eraser')

                // Mock invalid coordinates
                ; (CanvasUtils.screenToCanvasCoordinates as MockedFunction<any>).mockReturnValue({
                    x: NaN,
                    y: NaN
                })

            const canvas = wrapper.find('canvas')
            await expect(canvas.trigger('mousedown', { clientX: -100, clientY: -100 })).resolves.not.toThrow()
        })
    })
})