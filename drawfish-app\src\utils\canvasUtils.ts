import type { DrawingPoint, ToolSettings, CanvasBounds } from '@/types'

/**
 * Canvas utilities for drawing operations
 */
export class CanvasUtils {
  /**
   * Initialize canvas with proper settings
   */
  static initializeCanvas(canvas: HTMLCanvasElement, width: number, height: number): CanvasRenderingContext2D | null {
    if (!canvas) {
      console.error('Canvas element is required')
      return null
    }

    // Set canvas dimensions
    canvas.width = width
    canvas.height = height
    
    // Get 2D context
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error('Failed to get 2D context from canvas')
      return null
    }

    // Set default canvas properties
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'
    ctx.imageSmoothingEnabled = true
    
    // Clear canvas with white background
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, width, height)
    
    return ctx
  }

  /**
   * Start a new drawing path
   */
  static startDrawing(ctx: CanvasRenderingContext2D, point: DrawingPoint, settings: ToolSettings): void {
    if (!ctx) return

    ctx.beginPath()
    ctx.moveTo(point.x, point.y)
    
    // Apply tool settings
    this.applyToolSettings(ctx, settings)
  }

  /**
   * Continue drawing path
   */
  static continueDrawing(ctx: CanvasRenderingContext2D, point: DrawingPoint, settings: ToolSettings): void {
    if (!ctx) return

    // Apply current tool settings before drawing
    this.applyToolSettings(ctx, settings)
    
    ctx.lineTo(point.x, point.y)
    ctx.stroke()
    
    // Move to current point for next segment
    ctx.beginPath()
    ctx.moveTo(point.x, point.y)
  }

  /**
   * End current drawing path
   */
  static endDrawing(ctx: CanvasRenderingContext2D): void {
    if (!ctx) return
    
    ctx.closePath()
  }

  /**
   * Apply tool settings to context
   */
  private static applyToolSettings(ctx: CanvasRenderingContext2D, settings: ToolSettings): void {
    ctx.strokeStyle = settings.color
    ctx.lineWidth = settings.size
    ctx.globalAlpha = settings.opacity
    ctx.globalCompositeOperation = settings.color === '#FFFFFF' ? 'destination-out' : 'source-over'
  }

  /**
   * Erase at specific point
   */
  static eraseAt(ctx: CanvasRenderingContext2D, point: DrawingPoint, size: number): void {
    if (!ctx) return

    // Validate input parameters
    if (isNaN(point.x) || isNaN(point.y) || size <= 0 || isNaN(size)) {
      return
    }

    const previousCompositeOperation = ctx.globalCompositeOperation
    const previousFillStyle = ctx.fillStyle
    
    // Set eraser mode
    ctx.globalCompositeOperation = 'destination-out'
    ctx.fillStyle = 'rgba(0,0,0,1)' // Color doesn't matter in destination-out mode
    
    ctx.beginPath()
    ctx.arc(point.x, point.y, size / 2, 0, Math.PI * 2)
    ctx.fill()
    
    // Restore previous settings
    ctx.globalCompositeOperation = previousCompositeOperation
    ctx.fillStyle = previousFillStyle
  }

  /**
   * Erase along a path (for smooth erasing)
   */
  static eraseAlongPath(ctx: CanvasRenderingContext2D, fromPoint: DrawingPoint, toPoint: DrawingPoint, size: number): void {
    if (!ctx) return

    // Validate input parameters
    if (isNaN(fromPoint.x) || isNaN(fromPoint.y) || isNaN(toPoint.x) || isNaN(toPoint.y) || size <= 0 || isNaN(size)) {
      return
    }

    const previousCompositeOperation = ctx.globalCompositeOperation
    const previousStrokeStyle = ctx.strokeStyle
    const previousLineWidth = ctx.lineWidth
    const previousLineCap = ctx.lineCap
    
    // Set eraser mode
    ctx.globalCompositeOperation = 'destination-out'
    ctx.strokeStyle = 'rgba(0,0,0,1)' // Color doesn't matter in destination-out mode
    ctx.lineWidth = size
    ctx.lineCap = 'round'
    
    ctx.beginPath()
    ctx.moveTo(fromPoint.x, fromPoint.y)
    ctx.lineTo(toPoint.x, toPoint.y)
    ctx.stroke()
    
    // Restore previous settings
    ctx.globalCompositeOperation = previousCompositeOperation
    ctx.strokeStyle = previousStrokeStyle
    ctx.lineWidth = previousLineWidth
    ctx.lineCap = previousLineCap
  }

  /**
   * Clear entire canvas
   */
  static clearCanvas(ctx: CanvasRenderingContext2D, bounds: CanvasBounds): void {
    if (!ctx) return

    ctx.clearRect(0, 0, bounds.width, bounds.height)
    
    // Fill with white background
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, bounds.width, bounds.height)
  }

  /**
   * Flip canvas horizontally
   */
  static flipCanvas(ctx: CanvasRenderingContext2D, bounds: CanvasBounds): void {
    if (!ctx) return

    // Get current canvas content
    const imageData = ctx.getImageData(0, 0, bounds.width, bounds.height)
    
    // Clear canvas
    ctx.clearRect(0, 0, bounds.width, bounds.height)
    
    // Fill with white background
    ctx.fillStyle = '#FFFFFF'
    ctx.fillRect(0, 0, bounds.width, bounds.height)
    
    // Apply horizontal flip transformation
    ctx.save()
    ctx.scale(-1, 1)
    ctx.putImageData(imageData, -bounds.width, 0)
    ctx.restore()
  }

  /**
   * Get image data from canvas
   */
  static getImageData(ctx: CanvasRenderingContext2D, bounds: CanvasBounds): ImageData {
    return ctx.getImageData(0, 0, bounds.width, bounds.height)
  }

  /**
   * Set image data to canvas
   */
  static setImageData(ctx: CanvasRenderingContext2D, imageData: ImageData, x: number = 0, y: number = 0): void {
    if (!ctx || !imageData) return
    
    ctx.putImageData(imageData, x, y)
  }

  /**
   * Convert canvas to base64 string
   */
  static canvasToBase64(canvas: HTMLCanvasElement, type: string = 'image/png', quality?: number): string {
    return canvas.toDataURL(type, quality)
  }

  /**
   * Get canvas bounds
   */
  static getCanvasBounds(canvas: HTMLCanvasElement): CanvasBounds {
    const rect = canvas.getBoundingClientRect()
    return {
      width: canvas.width,
      height: canvas.height,
      offsetX: rect.left,
      offsetY: rect.top
    }
  }

  /**
   * Convert screen coordinates to canvas coordinates
   */
  static screenToCanvasCoordinates(
    screenX: number, 
    screenY: number, 
    canvas: HTMLCanvasElement
  ): { x: number, y: number } {
    const rect = canvas.getBoundingClientRect()
    const scaleX = canvas.width / rect.width
    const scaleY = canvas.height / rect.height
    
    return {
      x: (screenX - rect.left) * scaleX,
      y: (screenY - rect.top) * scaleY
    }
  }

  /**
   * Check if point is within canvas bounds
   */
  static isPointInBounds(point: DrawingPoint, bounds: CanvasBounds): boolean {
    return point.x >= 0 && point.x <= bounds.width && 
           point.y >= 0 && point.y <= bounds.height
  }
}

/**
 * Drawing history manager class
 */
export class DrawingHistoryManager {
  private history: ImageData[] = []
  private currentIndex: number = -1
  private maxHistorySize: number = 50

  constructor(maxHistorySize: number = 50) {
    this.maxHistorySize = maxHistorySize
  }

  /**
   * Add new state to history
   */
  addToHistory(imageData: ImageData): void {
    // Remove any redo history when adding new state
    if (this.currentIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.currentIndex + 1)
    }
    
    this.history.push(imageData)
    this.currentIndex = this.history.length - 1
    
    // Limit history size to prevent memory issues
    if (this.history.length > this.maxHistorySize) {
      this.history.shift()
      this.currentIndex = Math.max(0, this.currentIndex - 1)
    }
  }

  /**
   * Undo last action
   */
  undo(): ImageData | null {
    if (this.canUndo()) {
      this.currentIndex--
      return this.history[this.currentIndex]
    }
    return null
  }

  /**
   * Redo last undone action
   */
  redo(): ImageData | null {
    if (this.canRedo()) {
      this.currentIndex++
      return this.history[this.currentIndex]
    }
    return null
  }

  /**
   * Check if undo is possible
   */
  canUndo(): boolean {
    return this.currentIndex > 0
  }

  /**
   * Check if redo is possible
   */
  canRedo(): boolean {
    return this.currentIndex < this.history.length - 1
  }

  /**
   * Clear all history
   */
  clearHistory(): void {
    this.history = []
    this.currentIndex = -1
  }

  /**
   * Get current history length
   */
  getHistoryLength(): number {
    return this.history.length
  }

  /**
   * Get current history index
   */
  getCurrentIndex(): number {
    return this.currentIndex
  }

  /**
   * Get current state
   */
  getCurrentState(): ImageData | null {
    if (this.currentIndex >= 0 && this.currentIndex < this.history.length) {
      return this.history[this.currentIndex]
    }
    return null
  }
}

/**
 * Drawing path recorder for smooth drawing
 */
export class DrawingPathRecorder {
  private currentPath: DrawingPoint[] = []
  private isRecording: boolean = false
  private lastPoint: DrawingPoint | null = null

  /**
   * Start recording a new path
   */
  startPath(point: DrawingPoint): void {
    this.currentPath = [point]
    this.lastPoint = point
    this.isRecording = true
  }

  /**
   * Add point to current path
   */
  addPoint(point: DrawingPoint): void {
    if (!this.isRecording) return

    // Add smoothing by checking distance from last point
    if (this.lastPoint) {
      const distance = Math.sqrt(
        Math.pow(point.x - this.lastPoint.x, 2) + 
        Math.pow(point.y - this.lastPoint.y, 2)
      )
      
      // Only add point if it's far enough from the last one
      if (distance > 2) {
        this.currentPath.push(point)
        this.lastPoint = point
      }
    } else {
      this.currentPath.push(point)
      this.lastPoint = point
    }
  }

  /**
   * End current path recording
   */
  endPath(): DrawingPoint[] {
    this.isRecording = false
    const path = [...this.currentPath]
    this.currentPath = []
    this.lastPoint = null
    return path
  }

  /**
   * Get current path
   */
  getCurrentPath(): DrawingPoint[] {
    return [...this.currentPath]
  }

  /**
   * Check if currently recording
   */
  isCurrentlyRecording(): boolean {
    return this.isRecording
  }

  /**
   * Clear current path
   */
  clearCurrentPath(): void {
    this.currentPath = []
    this.lastPoint = null
    this.isRecording = false
  }
}