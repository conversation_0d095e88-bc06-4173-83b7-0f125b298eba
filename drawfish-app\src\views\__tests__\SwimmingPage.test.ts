import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import SwimmingPage from '../SwimmingPage.vue'
import { useFishStore } from '@/stores/fishStore'
import type { FishData } from '@/types'

// Mock Vue Router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock components
vi.mock('@/components/FishTank.vue', () => ({
  default: {
    name: 'FishTank',
    template: '<div class="fish-tank-mock"><slot></slot></div>',
    props: ['width', 'height']
  }
}))

vi.mock('@/components/SwimmingFish.vue', () => ({
  default: {
    name: 'SwimmingFish',
    template: '<div class="swimming-fish-mock"></div>',
    props: ['fishData', 'bounds', 'autoStart', 'showName', 'interactive'],
    emits: ['update:fishData', 'fishClick', 'animationStart', 'animationStop'],
    methods: {
      startAnimation: vi.fn(),
      pauseAnimation: vi.fn(),
      resumeAnimation: vi.fn(),
      stopAnimation: vi.fn()
    }
  }
}))

describe('SwimmingPage', () => {
  let fishStore: ReturnType<typeof useFishStore>
  let mockFish: FishData

  beforeEach(() => {
    setActivePinia(createPinia())
    fishStore = useFishStore()
    
    mockFish = {
      id: 'test-fish-1',
      name: 'Test Fish',
      imageData: 'data:image/png;base64,test',
      createdAt: new Date('2023-01-01'),
      dimensions: { width: 100, height: 80 },
      position: { x: 100, y: 100 },
      velocity: { x: 2, y: 1 },
      animationState: { tailPhase: 0, direction: 1 }
    }
    
    // Reset mocks
    mockPush.mockClear()
  })

  it('renders correctly with fish', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.swimming-page').exists()).toBe(true)
    expect(wrapper.find('.page-header').exists()).toBe(true)
    expect(wrapper.find('.main-content').exists()).toBe(true)
    expect(wrapper.find('.fish-tank-mock').exists()).toBe(true)
    expect(wrapper.find('.swimming-fish-mock').exists()).toBe(true)
  })

  it('renders no fish message when no fish available', () => {
    fishStore.setCurrentFish(null)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.no-fish-message').exists()).toBe(true)
    expect(wrapper.find('.swimming-fish-mock').exists()).toBe(false)
    expect(wrapper.text()).toContain('还没有鱼在游泳')
  })

  it('displays correct page title with fish name', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.page-title').text()).toBe('Test Fish 在游泳!')
  })

  it('displays default page title without fish', () => {
    fishStore.setCurrentFish(null)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.page-title').text()).toBe('观看你的鱼游泳!')
  })

  it('navigates back to drawing page when back button clicked', async () => {
    const wrapper = mount(SwimmingPage)
    
    await wrapper.find('.back-button').trigger('click')
    
    expect(mockPush).toHaveBeenCalledWith('/')
  })

  it('navigates back when create fish button clicked', async () => {
    fishStore.setCurrentFish(null)
    
    const wrapper = mount(SwimmingPage)
    
    await wrapper.find('.create-fish-button').trigger('click')
    
    expect(mockPush).toHaveBeenCalledWith('/')
  })

  it('shows animation control button when fish is present', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.animation-button').exists()).toBe(true)
  })

  it('hides animation control button when no fish', () => {
    fishStore.setCurrentFish(null)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.animation-button').exists()).toBe(false)
  })

  it('displays fish information panel when fish is present', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    const infoPanel = wrapper.find('.fish-info-panel')
    expect(infoPanel.exists()).toBe(true)
    expect(infoPanel.text()).toContain('Test Fish')
    expect(infoPanel.text()).toContain('100 × 80')
  })

  it('hides fish information panel when no fish', () => {
    fishStore.setCurrentFish(null)
    
    const wrapper = mount(SwimmingPage)
    
    expect(wrapper.find('.fish-info-panel').exists()).toBe(false)
  })

  it('passes correct props to FishTank component', () => {
    const wrapper = mount(SwimmingPage)
    
    const fishTank = wrapper.findComponent({ name: 'FishTank' })
    expect(fishTank.exists()).toBe(true)
    expect(fishTank.props('width')).toBeGreaterThan(0)
    expect(fishTank.props('height')).toBeGreaterThan(0)
  })

  it('passes correct props to SwimmingFish component', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    expect(swimmingFish.exists()).toBe(true)
    expect(swimmingFish.props('fishData')).toEqual(mockFish)
    expect(swimmingFish.props('autoStart')).toBe(true)
    expect(swimmingFish.props('showName')).toBe(true)
    expect(swimmingFish.props('interactive')).toBe(true)
    expect(swimmingFish.props('bounds')).toEqual(
      expect.objectContaining({
        width: expect.any(Number),
        height: expect.any(Number),
        offsetX: 20,
        offsetY: 20
      })
    )
  })

  it('handles fish data updates', async () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    
    const updatedFish = { ...mockFish, position: { x: 200, y: 150 } }
    await swimmingFish.vm.$emit('update:fishData', updatedFish)
    
    expect(fishStore.currentFish?.position).toEqual({ x: 200, y: 150 })
  })

  it('handles fish click events', async () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    
    // Mock console.log to verify the click handler
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})
    
    await swimmingFish.vm.$emit('fishClick', mockFish)
    
    expect(consoleSpy).toHaveBeenCalledWith('Fish clicked:', 'Test Fish')
    
    consoleSpy.mockRestore()
  })

  it('handles animation start events', async () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    
    await swimmingFish.vm.$emit('animationStart')
    
    expect(fishStore.isSwimming).toBe(true)
  })

  it('handles animation stop events', async () => {
    fishStore.setCurrentFish(mockFish)
    fishStore.startSwimming()
    
    const wrapper = mount(SwimmingPage)
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    
    await swimmingFish.vm.$emit('animationStop')
    
    expect(fishStore.isSwimming).toBe(false)
  })

  it('formats date correctly in fish info panel', () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    const infoPanel = wrapper.find('.fish-info-panel')
    expect(infoPanel.text()).toContain('2023')
  })

  it('shows correct animation status in fish info panel', async () => {
    fishStore.setCurrentFish(mockFish)
    
    const wrapper = mount(SwimmingPage)
    
    // Initially should show 静止
    expect(wrapper.find('.detail-value.status').text()).toBe('静止')
    expect(wrapper.find('.detail-value.status').classes()).not.toContain('active')
    
    // Simulate animation start
    const swimmingFish = wrapper.findComponent({ name: 'SwimmingFish' })
    await swimmingFish.vm.$emit('animationStart')
    await wrapper.vm.$nextTick()
    
    expect(wrapper.find('.detail-value.status').text()).toBe('游泳中')
    expect(wrapper.find('.detail-value.status').classes()).toContain('active')
  })

  it('calculates fish bounds correctly', () => {
    const wrapper = mount(SwimmingPage)
    
    // Access the computed property through the component instance
    const bounds = (wrapper.vm as any).fishBounds
    
    expect(bounds).toEqual(
      expect.objectContaining({
        width: expect.any(Number),
        height: expect.any(Number),
        offsetX: 20,
        offsetY: 20
      })
    )
    
    // Bounds should be smaller than tank dimensions to leave margins
    expect(bounds.width).toBeLessThan((wrapper.vm as any).tankDimensions.width)
    expect(bounds.height).toBeLessThan((wrapper.vm as any).tankDimensions.height)
  })
})