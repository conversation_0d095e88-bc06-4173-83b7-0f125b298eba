import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { FishAnimator, createFishAnimator, createMultipleFishAnimators } from '../fishAnimator'
import type { FishData, CanvasBounds, Position, Velocity } from '../../types'

// Mock the animation utilities
vi.mock('../animationUtils', () => ({
  calculatePosition: vi.fn((pos, vel, dt) => ({
    x: pos.x + vel.x,
    y: pos.y + vel.y
  })),
  checkBoundaries: vi.fn((pos, bounds, dims) => ({
    position: pos,
    velocity: { x: 2, y: 1 }
  })),
  updateTailAnimation: vi.fn((phase, speed) => phase + 0.2),
  calculateTailOffset: vi.fn((phase) => Math.sin(phase) * 10),
  createAnimationLoop: vi.fn((callback) => ({
    start: vi.fn(),
    stop: vi.fn(),
    isRunning: vi.fn(() => false)
  })),
  generateRandomVelocity: vi.fn(() => ({ x: 2, y: 1 })),
  ANIMATION_CONFIG: {
    TARGET_FPS: 60,
    FRAME_TIME: 16.67,
    TAIL_SPEED: 0.2,
    TAIL_AMPLITUDE: 10,
    DEFAULT_VELOCITY: { x: 2, y: 1 },
    BOUNDARY_PADDING: 20
  }
}))

describe('FishAnimator', () => {
  let mockFishData: FishData
  let mockBounds: CanvasBounds
  let mockOnUpdate: ReturnType<typeof vi.fn>
  
  beforeEach(() => {
    vi.useFakeTimers()
    global.performance = {
      now: vi.fn(() => Date.now())
    } as any
    
    mockFishData = {
      id: 'test-fish',
      name: 'Test Fish',
      imageData: 'mock-image-data',
      createdAt: new Date(),
      dimensions: { width: 50, height: 30 },
      position: { x: 100, y: 100 },
      velocity: { x: 2, y: 1 },
      animationState: {
        tailPhase: 0,
        direction: 1
      }
    }
    
    mockBounds = {
      width: 800,
      height: 600,
      top: 0,
      left: 0
    }
    
    mockOnUpdate = vi.fn()
  })
  
  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  })
  
  describe('constructor', () => {
    it('should create fish animator with provided data', () => {
      const animator = new FishAnimator(mockFishData, mockBounds, mockOnUpdate)
      
      expect(animator).toBeInstanceOf(FishAnimator)
      expect(animator.getFishData()).toEqual(mockFishData)
    })
    
    it('should generate random velocity if fish has no velocity', () => {
      const fishWithoutVelocity = {
        ...mockFishData,
        velocity: { x: 0, y: 0 }
      }
      
      const animator = new FishAnimator(fishWithoutVelocity, mockBounds)
      const fishData = animator.getFishData()
      
      expect(fishData.velocity).toEqual({ x: 2, y: 1 }) // From mocked generateRandomVelocity
    })
  })
  
  describe('animation control', () => {
    it('should start and stop animation', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.startAnimation()
      expect(animator.isAnimating()).toBe(false) // Mocked to return false
      
      animator.stopAnimation()
      expect(animator.isAnimating()).toBe(false)
    })
    
    it('should pause and resume animation', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.pause()
      animator.resume()
      
      // Should not throw errors
      expect(true).toBe(true)
    })
  })
  
  describe('position and movement', () => {
    it('should update position', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.updatePosition(16.67)
      
      const position = animator.getPosition()
      expect(position.x).toBe(102) // 100 + 2 (from mocked calculatePosition)
      expect(position.y).toBe(101) // 100 + 1
    })
    
    it('should set and get position', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      const newPosition: Position = { x: 200, y: 150 }
      
      animator.setPosition(newPosition)
      
      expect(animator.getPosition()).toEqual(newPosition)
    })
    
    it('should set and get velocity', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      const newVelocity: Velocity = { x: 3, y: 2 }
      
      animator.setVelocity(newVelocity)
      
      expect(animator.getVelocity()).toEqual(newVelocity)
    })
  })
  
  describe('tail animation', () => {
    it('should update tail animation', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.updateTailAnimation(16.67)
      
      const animationState = animator.getAnimationState()
      expect(animationState.tailPhase).toBe(0.2) // From mocked updateTailAnimation
    })
    
    it('should update direction based on velocity', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      // Set negative x velocity
      animator.setVelocity({ x: -2, y: 1 })
      animator.updateTailAnimation()
      
      const animationState = animator.getAnimationState()
      expect(animationState.direction).toBe(-1)
    })
    
    it('should get tail offset', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      const tailOffset = animator.getTailOffset()
      
      expect(typeof tailOffset).toBe('number')
    })
  })
  
  describe('boundary handling', () => {
    it('should check boundaries', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.checkBoundaries()
      
      // Should not throw errors and should call the mocked function
      expect(true).toBe(true)
    })
    
    it('should update bounds', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      const newBounds: CanvasBounds = {
        width: 1000,
        height: 800,
        top: 0,
        left: 0
      }
      
      animator.updateBounds(newBounds)
      
      // Should not throw errors
      expect(true).toBe(true)
    })
    
    it('should constrain fish position when updating bounds', () => {
      const fishAtEdge = {
        ...mockFishData,
        position: { x: 750, y: 550 }
      }
      const animator = new FishAnimator(fishAtEdge, mockBounds)
      
      const smallerBounds: CanvasBounds = {
        width: 400,
        height: 300,
        top: 0,
        left: 0
      }
      
      animator.updateBounds(smallerBounds)
      
      const position = animator.getPosition()
      expect(position.x).toBeLessThanOrEqual(smallerBounds.width - fishAtEdge.dimensions.width - 20)
      expect(position.y).toBeLessThanOrEqual(smallerBounds.height - fishAtEdge.dimensions.height - 20)
    })
  })
  
  describe('callbacks and rendering', () => {
    it('should call onUpdate callback when rendering', () => {
      const animator = new FishAnimator(mockFishData, mockBounds, mockOnUpdate)
      
      animator.render()
      
      expect(mockOnUpdate).toHaveBeenCalledWith(expect.objectContaining({
        id: 'test-fish',
        name: 'Test Fish'
      }))
    })
    
    it('should not throw error when rendering without callback', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      expect(() => animator.render()).not.toThrow()
    })
  })
  
  describe('reset and destroy', () => {
    it('should reset fish to initial state', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      
      animator.reset()
      
      const fishData = animator.getFishData()
      expect(fishData.velocity).toEqual({ x: 2, y: 1 }) // From mocked generateRandomVelocity
      expect(fishData.animationState.tailPhase).toBe(0)
      expect(fishData.animationState.direction).toBe(1)
    })
    
    it('should reset with partial data', () => {
      const animator = new FishAnimator(mockFishData, mockBounds)
      const resetData = {
        position: { x: 300, y: 200 },
        velocity: { x: 5, y: 3 }
      }
      
      animator.reset(resetData)
      
      const fishData = animator.getFishData()
      expect(fishData.position).toEqual(resetData.position)
      expect(fishData.velocity).toEqual(resetData.velocity)
    })
    
    it('should destroy animator and clean up', () => {
      const animator = new FishAnimator(mockFishData, mockBounds, mockOnUpdate)
      
      animator.destroy()
      
      // Should not throw errors
      expect(true).toBe(true)
    })
  })
  
  describe('factory functions', () => {
    it('should create fish animator using factory function', () => {
      const animator = createFishAnimator(mockFishData, mockBounds, mockOnUpdate)
      
      expect(animator).toBeInstanceOf(FishAnimator)
    })
    
    it('should create multiple fish animators', () => {
      const fishArray = [mockFishData, { ...mockFishData, id: 'fish-2' }]
      const onUpdateMultiple = vi.fn()
      
      const animators = createMultipleFishAnimators(fishArray, mockBounds, onUpdateMultiple)
      
      expect(animators).toHaveLength(2)
      expect(animators[0]).toBeInstanceOf(FishAnimator)
      expect(animators[1]).toBeInstanceOf(FishAnimator)
      
      // Test callback with index
      animators[0].render()
      expect(onUpdateMultiple).toHaveBeenCalledWith(expect.any(Object), 0)
    })
  })
})