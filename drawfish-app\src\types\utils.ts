// Utility types and helper interfaces

/**
 * API response wrapper
 */
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * Fish API endpoints interface
 */
export interface FishApiInterface {
  saveFish: (fish: FishData) => Promise<ApiResponse<{ id: string }>>
  loadFish: (id: string) => Promise<ApiResponse<FishData>>
  loadAllFish: () => Promise<ApiResponse<FishData[]>>
  deleteFish: (id: string) => Promise<ApiResponse<boolean>>
}

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  FISH_COLLECTION: 'drawfish_collection',
  CURRENT_FISH: 'drawfish_current',
  DRAWING_SETTINGS: 'drawfish_settings',
  CANVAS_HISTORY: 'drawfish_history'
} as const

/**
 * Default values
 */
export const DEFAULT_VALUES = {
  BRUSH_SIZE: 5,
  COLOR: '#000000',
  TOOL: 'brush' as const,
  CANVAS_WIDTH: 800,
  CANVAS_HEIGHT: 600,
  ANIMATION_SPEED: 2,
  TAIL_AMPLITUDE: 10,
  MAX_HISTORY: 20
} as const

/**
 * Color palette
 */
export const COLOR_PALETTE = [
  '#000000', // Black
  '#FF0000', // Red
  '#00FF00', // Green
  '#0000FF', // Blue
  '#FFFF00', // Yellow
  '#FF00FF', // Magenta
  '#00FFFF', // Cyan
  '#FFA500', // Orange
  '#800080', // Purple
  '#FFC0CB', // Pink
  '#A52A2A', // Brown
  '#808080'  // Gray
] as const

/**
 * Event types for custom events
 */
export type DrawingEvent = 'start' | 'continue' | 'end' | 'cancel'
export type AnimationEvent = 'start' | 'pause' | 'resume' | 'stop' | 'complete'
export type NavigationEvent = 'to-drawing' | 'to-swimming' | 'back'

/**
 * Error types
 */
export interface DrawingError {
  type: 'canvas' | 'tool' | 'history' | 'save'
  message: string
  details?: any
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  drawingLatency: number
  animationFPS: number
  memoryUsage: number
  renderTime: number
}

import type { FishData } from './index'