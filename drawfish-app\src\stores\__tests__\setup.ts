import { vi, expect } from 'vitest'

// Mock ImageData for testing environment
class MockImageData {
  width: number
  height: number
  data: Uint8ClampedArray

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
}

// Make ImageData available globally in tests
vi.stubGlobal('ImageData', MockImageData)

// Add custom matchers
expect.extend({
  toBeOneOf(received: any, expected: any[]) {
    const pass = expected.includes(received)
    if (pass) {
      return {
        message: () => `expected ${received} not to be one of ${expected}`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected ${received} to be one of ${expected}`,
        pass: false,
      }
    }
  },
})

// Extend the expect interface
declare module 'vitest' {
  interface Assertion<T = any> {
    toBeOneOf(expected: any[]): T
  }
}