<template>
  <div class="size-slider">
    <div class="size-slider-header">
      <label for="brush-size-slider" class="size-label">线条粗细:</label>
      <div class="size-display">
        <span class="size-value">{{ currentSize }}</span>
        <span class="size-unit">px</span>
      </div>
    </div>
    
    <div class="slider-container">
      <input
        id="brush-size-slider"
        type="range"
        class="size-slider-input"
        :value="currentSize"
        :min="minSize"
        :max="maxSize"
        :step="1"
        @input="onSizeChange"
        :aria-label="`线条粗细: ${currentSize}像素`"
        :aria-valuemin="minSize"
        :aria-valuemax="maxSize"
        :aria-valuenow="currentSize"
      />
      
      <div class="slider-track-labels">
        <span class="track-label track-label-min">{{ minSize }}</span>
        <span class="track-label track-label-max">{{ maxSize }}</span>
      </div>
    </div>
    
    <div class="size-preview">
      <div class="preview-label">预览:</div>
      <div 
        class="size-preview-dot"
        :style="{ 
          width: `${Math.max(currentSize, 4)}px`, 
          height: `${Math.max(currentSize, 4)}px`,
          backgroundColor: previewColor
        }"
        :title="`线条粗细预览: ${currentSize}px`"
      ></div>
    </div>
    
    <div class="quick-sizes">
      <span class="quick-sizes-label">快速选择:</span>
      <div class="quick-size-buttons">
        <button
          v-for="size in quickSizes"
          :key="size"
          class="quick-size-button"
          :class="{ active: currentSize === size }"
          @click="selectQuickSize(size)"
          :title="`设置线条粗细为 ${size}px`"
          :aria-label="`设置线条粗细为 ${size}像素`"
        >
          {{ size }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useDrawingStore } from '@/stores/drawingStore'

// Props
interface Props {
  minSize?: number
  maxSize?: number
  previewColor?: string
}

const props = withDefaults(defineProps<Props>(), {
  minSize: 1,
  maxSize: 50,
  previewColor: '#000000'
})

// Emits
interface Emits {
  'size-changed': [size: number]
}

const emit = defineEmits<Emits>()

// Store
const drawingStore = useDrawingStore()

// Computed
const currentSize = computed(() => drawingStore.brushSize)

// Quick size options
const quickSizes = [2, 5, 10, 15, 25]

// Methods
function onSizeChange(event: Event) {
  const target = event.target as HTMLInputElement
  const newSize = parseInt(target.value, 10)
  
  if (!isNaN(newSize) && newSize >= props.minSize && newSize <= props.maxSize) {
    drawingStore.setBrushSize(newSize)
    emit('size-changed', newSize)
  }
}

function selectQuickSize(size: number) {
  drawingStore.setBrushSize(size)
  emit('size-changed', size)
}
</script>

<style scoped>
.size-slider {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  min-width: 280px;
}

.size-slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.size-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.size-display {
  display: flex;
  align-items: baseline;
  gap: 2px;
  font-weight: 600;
}

.size-value {
  font-size: 18px;
  color: #007bff;
}

.size-unit {
  font-size: 12px;
  color: #6c757d;
}

.slider-container {
  position: relative;
}

.size-slider-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #dee2e6;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.size-slider-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.size-slider-input::-webkit-slider-thumb:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.size-slider-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #007bff;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.size-slider-input::-moz-range-thumb:hover {
  background: #0056b3;
  transform: scale(1.1);
}

.size-slider-input:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.slider-track-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.track-label {
  font-size: 12px;
  color: #6c757d;
}

.size-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.preview-label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

.size-preview-dot {
  border-radius: 50%;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.quick-sizes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-sizes-label {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.quick-size-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.quick-size-button {
  padding: 6px 12px;
  border: 2px solid #dee2e6;
  border-radius: 4px;
  background: #ffffff;
  color: #495057;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 36px;
}

.quick-size-button:hover {
  border-color: #007bff;
  color: #007bff;
  transform: translateY(-1px);
}

.quick-size-button.active {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.quick-size-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .size-slider {
    min-width: auto;
    padding: 12px;
  }
  
  .size-slider-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .size-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .quick-size-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .quick-size-buttons {
    gap: 4px;
  }
  
  .quick-size-button {
    padding: 4px 8px;
    min-width: 32px;
  }
}
</style>