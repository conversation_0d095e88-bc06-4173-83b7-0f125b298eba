import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { DrawingTool, ToolSettings, FishData, FishStroke, DrawingPoint } from '@/types'

export const useDrawingStore = defineStore('drawing', () => {
  // State
  const currentTool = ref<DrawingTool>('brush')
  const currentColor = ref('#000000')
  const brushSize = ref(5)
  const canvasHistory = ref<ImageData[]>([])
  const historyIndex = ref(-1)
  const isDrawing = ref(false)
  const lastDrawnFish = ref<FishData | null>(null)
  const currentStrokes = ref<FishStroke[]>([])
  const currentStroke = ref<FishStroke | null>(null)
  
  // Tool settings computed property
  const toolSettings = computed<ToolSettings>(() => ({
    color: currentTool.value === 'eraser' ? '#FFFFFF' : currentColor.value,
    size: brushSize.value,
    opacity: 1
  }))

  // Getters
  const canUndo = computed(() => historyIndex.value > 0)
  const canRedo = computed(() => historyIndex.value < canvasHistory.value.length - 1)
  const hasContent = computed(() => canvasHistory.value.length > 0)
  const currentToolSettings = computed(() => toolSettings.value)

  // Actions
  function setTool(tool: DrawingTool) {
    currentTool.value = tool
  }

  function setColor(color: string) {
    if (currentTool.value !== 'eraser') {
      currentColor.value = color
    }
  }

  function setBrushSize(size: number) {
    if (size >= 1 && size <= 50) {
      brushSize.value = size
    }
  }

  function setToolSettings(settings: Partial<ToolSettings>) {
    if (settings.color && currentTool.value !== 'eraser') {
      currentColor.value = settings.color
    }
    if (settings.size) {
      setBrushSize(settings.size)
    }
  }

  function addToHistory(imageData: ImageData) {
    // Remove any redo history when adding new state
    if (historyIndex.value < canvasHistory.value.length - 1) {
      canvasHistory.value = canvasHistory.value.slice(0, historyIndex.value + 1)
    }
    
    canvasHistory.value.push(imageData)
    historyIndex.value = canvasHistory.value.length - 1
    
    // Limit history to prevent memory issues
    const maxHistory = 50
    if (canvasHistory.value.length > maxHistory) {
      canvasHistory.value.shift()
      historyIndex.value = Math.max(0, historyIndex.value - 1)
    }
  }

  function undo(): ImageData | null {
    if (canUndo.value) {
      historyIndex.value--
      return canvasHistory.value[historyIndex.value]
    }
    return null
  }

  function redo(): ImageData | null {
    if (canRedo.value) {
      historyIndex.value++
      return canvasHistory.value[historyIndex.value]
    }
    return null
  }

  function clearCanvas() {
    canvasHistory.value = []
    historyIndex.value = -1
  }

  function flipCanvas() {
    // This will be implemented when we create the canvas utilities
    // For now, we'll add a placeholder that can be extended
    console.log('Flip canvas functionality - to be implemented with canvas utilities')
  }

  function setDrawingState(drawing: boolean) {
    isDrawing.value = drawing
  }

  function setLastDrawnFish(fish: FishData | null) {
    lastDrawnFish.value = fish
  }

  function startStroke(point: DrawingPoint) {
    if (currentTool.value === 'eraser') return // Don't record eraser strokes
    
    currentStroke.value = {
      id: `stroke_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      points: [point],
      color: currentColor.value,
      size: brushSize.value,
      tool: currentTool.value,
      timestamp: Date.now()
    }
  }

  function addPointToStroke(point: DrawingPoint) {
    if (currentStroke.value && currentTool.value !== 'eraser') {
      currentStroke.value.points.push(point)
    }
  }

  function finishStroke() {
    if (currentStroke.value && currentStroke.value.points.length > 1) {
      currentStrokes.value.push({ ...currentStroke.value })
    }
    currentStroke.value = null
  }

  function clearStrokes() {
    currentStrokes.value = []
    currentStroke.value = null
  }

  function getStrokes(): FishStroke[] {
    return [...currentStrokes.value]
  }

  function resetDrawingState() {
    currentTool.value = 'brush'
    currentColor.value = '#000000'
    brushSize.value = 5
    canvasHistory.value = []
    historyIndex.value = -1
    isDrawing.value = false
    lastDrawnFish.value = null
    currentStrokes.value = []
    currentStroke.value = null
  }

  return {
    // State
    currentTool,
    currentColor,
    brushSize,
    canvasHistory,
    historyIndex,
    isDrawing,
    lastDrawnFish,
    toolSettings,
    currentStrokes,
    currentStroke,
    
    // Getters
    canUndo,
    canRedo,
    hasContent,
    currentToolSettings,
    
    // Actions
    setTool,
    setColor,
    setBrushSize,
    setToolSettings,
    addToHistory,
    undo,
    redo,
    clearCanvas,
    flipCanvas,
    setDrawingState,
    setLastDrawnFish,
    resetDrawingState,
    startStroke,
    addPointToStroke,
    finishStroke,
    clearStrokes,
    getStrokes
  }
})