// Store-specific types and interfaces

import type { FishData, DrawingTool, ToolSettings, DrawingPath } from './index'

/**
 * Drawing store state interface
 */
export interface DrawingStoreState {
  currentTool: DrawingTool
  currentColor: string
  brushSize: number
  canvasHistory: ImageData[]
  historyIndex: number
  isDrawing: boolean
  lastDrawnFish: FishData | null
  toolSettings: ToolSettings
}

/**
 * Fish store state interface
 */
export interface FishStoreState {
  currentFish: FishData | null
  fishCollection: FishData[]
  isSwimming: boolean
  swimBounds: {
    width: number
    height: number
  }
}

/**
 * Drawing store actions interface
 */
export interface DrawingStoreActions {
  setTool: (tool: DrawingTool) => void
  setColor: (color: string) => void
  setBrushSize: (size: number) => void
  setToolSettings: (settings: Partial<ToolSettings>) => void
  addToHistory: (imageData: ImageData) => void
  undo: () => ImageData | null
  redo: () => ImageData | null
  clearCanvas: () => void
  flipCanvas: () => void
  setDrawingState: (isDrawing: boolean) => void
  setLastDrawnFish: (fish: FishData | null) => void
  resetDrawingState: () => void
}

/**
 * Fish store actions interface
 */
export interface FishStoreActions {
  createFish: (imageData: ImageData | string, name: string) => FishData
  setCurrentFish: (fish: FishData | null) => void
  addToCollection: (fish: FishData) => void
  removeFromCollection: (fishId: string) => void
  startSwimming: () => void
  stopSwimming: () => void
  updateFishPosition: (fishId: string, position: { x: number, y: number }) => void
  updateFishVelocity: (fishId: string, velocity: { x: number, y: number }) => void
  updateAnimationState: (fishId: string, animationState: Partial<{ tailPhase: number, direction: number }>) => void
  setSwimBounds: (bounds: { width: number, height: number }) => void
  clearCollection: () => void
}

/**
 * Store getters interface
 */
export interface DrawingStoreGetters {
  canUndo: () => boolean
  canRedo: () => boolean
  hasContent: () => boolean
  currentToolSettings: () => ToolSettings
}

export interface FishStoreGetters {
  hasFish: () => boolean
  fishCount: () => number
  getCurrentFishName: () => string | null
}