/**
 * Fish outline extraction utilities
 * Extracts vector outlines from raster fish drawings
 */

export interface OutlinePoint {
  x: number
  y: number
}

export interface FishOutline {
  points: OutlinePoint[]
  color: string
  thickness: number
}

export class OutlineExtractor {
  /**
   * Extract fish outline from ImageData
   */
  static extractOutlines(imageData: ImageData): FishOutline[] {
    const { width, height, data } = imageData
    const outlines: FishOutline[] = []
    const visited = new Set<string>()
    
    // Find all connected components (different colored regions)
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4
        const alpha = data[index + 3]
        
        // Skip transparent pixels
        if (alpha === 0) continue
        
        const key = `${x},${y}`
        if (visited.has(key)) continue
        
        // Get the color of this pixel
        const r = data[index]
        const g = data[index + 1]
        const b = data[index + 2]
        const color = `rgb(${r},${g},${b})`
        
        // Find all connected pixels of the same color
        const component = this.findConnectedComponent(data, width, height, x, y, visited)
        
        if (component.length > 3) { // Only process components with more than 3 pixels
          const outline = this.traceOutline(component, width, height)
          if (outline.length > 3) {
            outlines.push({
              points: outline,
              color,
              thickness: this.estimateThickness(component)
            })
          }
        }
      }
    }
    
    return outlines
  }
  
  /**
   * Find all pixels connected to the starting point with the same color
   */
  private static findConnectedComponent(
    data: Uint8ClampedArray,
    width: number,
    height: number,
    startX: number,
    startY: number,
    visited: Set<string>
  ): OutlinePoint[] {
    const startIndex = (startY * width + startX) * 4
    const targetR = data[startIndex]
    const targetG = data[startIndex + 1]
    const targetB = data[startIndex + 2]
    const targetA = data[startIndex + 3]
    
    const component: OutlinePoint[] = []
    const stack = [{ x: startX, y: startY }]
    
    while (stack.length > 0) {
      const { x, y } = stack.pop()!
      const key = `${x},${y}`
      
      if (visited.has(key) || x < 0 || x >= width || y < 0 || y >= height) {
        continue
      }
      
      const index = (y * width + x) * 4
      const r = data[index]
      const g = data[index + 1]
      const b = data[index + 2]
      const a = data[index + 3]
      
      // Check if pixel matches the target color (with some tolerance)
      const colorDistance = Math.sqrt(
        Math.pow(r - targetR, 2) + 
        Math.pow(g - targetG, 2) + 
        Math.pow(b - targetB, 2)
      )
      
      if (a === 0 || colorDistance > 30) continue // Color tolerance
      
      visited.add(key)
      component.push({ x, y })
      
      // Add 8-connected neighbors
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          if (dx === 0 && dy === 0) continue
          stack.push({ x: x + dx, y: y + dy })
        }
      }
    }
    
    return component
  }
  
  /**
   * Trace the outline of a connected component
   */
  private static traceOutline(component: OutlinePoint[], width: number, height: number): OutlinePoint[] {
    if (component.length === 0) return []
    
    // Create a set for fast lookup
    const pixelSet = new Set(component.map(p => `${p.x},${p.y}`))
    
    // Find boundary pixels (pixels that have at least one non-component neighbor)
    const boundaryPixels: OutlinePoint[] = []
    
    for (const pixel of component) {
      const { x, y } = pixel
      let isBoundary = false
      
      // Check 8 neighbors
      for (let dx = -1; dx <= 1; dx++) {
        for (let dy = -1; dy <= 1; dy++) {
          if (dx === 0 && dy === 0) continue
          const neighborKey = `${x + dx},${y + dy}`
          if (!pixelSet.has(neighborKey)) {
            isBoundary = true
            break
          }
        }
        if (isBoundary) break
      }
      
      if (isBoundary) {
        boundaryPixels.push(pixel)
      }
    }
    
    if (boundaryPixels.length === 0) return component.slice(0, 10)
    
    // Sort boundary pixels to create a proper outline
    const sortedBoundary = this.sortBoundaryPixels(boundaryPixels)
    
    // Simplify the outline using Douglas-Peucker algorithm
    return this.simplifyOutline(sortedBoundary, 2.0)
  }
  
  /**
   * Sort boundary pixels to create a continuous outline
   */
  private static sortBoundaryPixels(pixels: OutlinePoint[]): OutlinePoint[] {
    if (pixels.length <= 2) return pixels
    
    // Find the centroid
    const centerX = pixels.reduce((sum, p) => sum + p.x, 0) / pixels.length
    const centerY = pixels.reduce((sum, p) => sum + p.y, 0) / pixels.length
    
    // Sort by angle from center
    return pixels.sort((a, b) => {
      const angleA = Math.atan2(a.y - centerY, a.x - centerX)
      const angleB = Math.atan2(b.y - centerY, b.x - centerX)
      return angleA - angleB
    })
  }
  
  /**
   * Simplify outline using Douglas-Peucker algorithm
   */
  private static simplifyOutline(points: OutlinePoint[], tolerance: number): OutlinePoint[] {
    if (points.length <= 2) return points
    
    return this.douglasPeucker(points, tolerance)
  }
  
  /**
   * Douglas-Peucker line simplification algorithm
   */
  private static douglasPeucker(points: OutlinePoint[], tolerance: number): OutlinePoint[] {
    if (points.length <= 2) return points
    
    // Find the point with maximum distance from the line between first and last points
    let maxDistance = 0
    let maxIndex = 0
    
    const start = points[0]
    const end = points[points.length - 1]
    
    for (let i = 1; i < points.length - 1; i++) {
      const distance = this.pointToLineDistance(points[i], start, end)
      if (distance > maxDistance) {
        maxDistance = distance
        maxIndex = i
      }
    }
    
    // If max distance is greater than tolerance, recursively simplify
    if (maxDistance > tolerance) {
      const leftPart = this.douglasPeucker(points.slice(0, maxIndex + 1), tolerance)
      const rightPart = this.douglasPeucker(points.slice(maxIndex), tolerance)
      
      // Combine results (remove duplicate point at junction)
      return [...leftPart.slice(0, -1), ...rightPart]
    } else {
      // All points are within tolerance, return just the endpoints
      return [start, end]
    }
  }
  
  /**
   * Calculate distance from point to line
   */
  private static pointToLineDistance(point: OutlinePoint, lineStart: OutlinePoint, lineEnd: OutlinePoint): number {
    const A = point.x - lineStart.x
    const B = point.y - lineStart.y
    const C = lineEnd.x - lineStart.x
    const D = lineEnd.y - lineStart.y
    
    const dot = A * C + B * D
    const lenSq = C * C + D * D
    
    if (lenSq === 0) {
      // Line start and end are the same point
      return Math.sqrt(A * A + B * B)
    }
    
    const param = dot / lenSq
    let xx: number, yy: number
    
    if (param < 0) {
      xx = lineStart.x
      yy = lineStart.y
    } else if (param > 1) {
      xx = lineEnd.x
      yy = lineEnd.y
    } else {
      xx = lineStart.x + param * C
      yy = lineStart.y + param * D
    }
    
    const dx = point.x - xx
    const dy = point.y - yy
    return Math.sqrt(dx * dx + dy * dy)
  }
  
  /**
   * Estimate the thickness of a stroke based on the component
   */
  private static estimateThickness(component: OutlinePoint[]): number {
    if (component.length === 0) return 2
    
    // Simple heuristic: use the square root of the area divided by perimeter
    const area = component.length
    
    // Estimate perimeter by finding boundary pixels
    const pixelSet = new Set(component.map(p => `${p.x},${p.y}`))
    let boundaryCount = 0
    
    for (const pixel of component) {
      const { x, y } = pixel
      let isBoundary = false
      
      // Check 4-connected neighbors
      const neighbors = [
        { x: x + 1, y },
        { x: x - 1, y },
        { x, y: y + 1 },
        { x, y: y - 1 }
      ]
      
      for (const neighbor of neighbors) {
        if (!pixelSet.has(`${neighbor.x},${neighbor.y}`)) {
          isBoundary = true
          break
        }
      }
      
      if (isBoundary) boundaryCount++
    }
    
    const perimeter = Math.max(boundaryCount, 1)
    const thickness = Math.max(1, Math.min(8, (2 * area) / perimeter))
    
    return Math.round(thickness)
  }
  
  /**
   * Convert outlines to SVG path strings
   */
  static outlinesToSVGPaths(outlines: FishOutline[]): string[] {
    return outlines.map(outline => {
      if (outline.points.length === 0) return ''
      
      let path = `M ${outline.points[0].x} ${outline.points[0].y}`
      
      for (let i = 1; i < outline.points.length; i++) {
        path += ` L ${outline.points[i].x} ${outline.points[i].y}`
      }
      
      path += ' Z' // Close the path
      return path
    })
  }
  
  /**
   * Create Path2D objects from outlines
   */
  static outlinesToPaths(outlines: FishOutline[]): Path2D[] {
    return outlines.map(outline => {
      const path = new Path2D()
      
      if (outline.points.length === 0) return path
      
      path.moveTo(outline.points[0].x, outline.points[0].y)
      
      for (let i = 1; i < outline.points.length; i++) {
        path.lineTo(outline.points[i].x, outline.points[i].y)
      }
      
      path.closePath()
      return path
    })
  }
}