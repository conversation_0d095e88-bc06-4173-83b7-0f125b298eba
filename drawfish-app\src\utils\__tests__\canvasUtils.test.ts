import { describe, it, expect, beforeEach, vi } from 'vitest'
import { CanvasUtils, DrawingHistoryManager, DrawingPathRecorder } from '../canvasUtils'
import type { DrawingPoint, ToolSettings, CanvasBounds } from '@/types'

// Mock ImageData for testing environment
class MockImageData {
  width: number
  height: number
  data: Uint8ClampedArray

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
}

// Make ImageData available globally for tests
global.ImageData = MockImageData as any

// Mock HTMLCanvasElement and CanvasRenderingContext2D
class MockCanvasRenderingContext2D {
  lineCap: string = 'round'
  lineJoin: string = 'round'
  imageSmoothingEnabled: boolean = true
  fillStyle: string = '#000000'
  strokeStyle: string = '#000000'
  lineWidth: number = 1
  globalAlpha: number = 1
  globalCompositeOperation: string = 'source-over'

  beginPath = vi.fn()
  moveTo = vi.fn()
  lineTo = vi.fn()
  stroke = vi.fn()
  closePath = vi.fn()
  arc = vi.fn()
  fill = vi.fn()
  clearRect = vi.fn()
  fillRect = vi.fn()
  save = vi.fn()
  restore = vi.fn()
  scale = vi.fn()
  putImageData = vi.fn()
  getImageData = vi.fn(() => new MockImageData(100, 100))
}

class MockHTMLCanvasElement {
  width: number = 0
  height: number = 0
  
  getContext = vi.fn(() => new MockCanvasRenderingContext2D())
  getBoundingClientRect = vi.fn(() => ({
    top: 0,
    left: 0,
    width: 100,
    height: 100
  }))
  toDataURL = vi.fn(() => 'data:image/png;base64,mock')
}

describe('CanvasUtils', () => {
  let canvas: MockHTMLCanvasElement
  let ctx: MockCanvasRenderingContext2D

  beforeEach(() => {
    canvas = new MockHTMLCanvasElement()
    ctx = new MockCanvasRenderingContext2D()
    vi.clearAllMocks()
  })

  describe('initializeCanvas', () => {
    it('should initialize canvas with correct dimensions', () => {
      const result = CanvasUtils.initializeCanvas(canvas as any, 800, 600)
      
      expect(canvas.width).toBe(800)
      expect(canvas.height).toBe(600)
      expect(result).toBeTruthy()
    })

    it('should set proper canvas context properties', () => {
      CanvasUtils.initializeCanvas(canvas as any, 800, 600)
      
      expect(ctx.lineCap).toBe('round')
      expect(ctx.lineJoin).toBe('round')
      expect(ctx.imageSmoothingEnabled).toBe(true)
    })

    it('should return null if canvas is null', () => {
      const result = CanvasUtils.initializeCanvas(null as any, 800, 600)
      expect(result).toBeNull()
    })

    it('should return null if context is not available', () => {
      canvas.getContext = vi.fn(() => null)
      const result = CanvasUtils.initializeCanvas(canvas as any, 800, 600)
      expect(result).toBeNull()
    })
  })

  describe('startDrawing', () => {
    it('should begin path and move to point', () => {
      const point: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const settings: ToolSettings = { color: '#FF0000', size: 5, opacity: 1 }
      
      CanvasUtils.startDrawing(ctx as any, point, settings)
      
      expect(ctx.beginPath).toHaveBeenCalled()
      expect(ctx.moveTo).toHaveBeenCalledWith(10, 20)
      expect(ctx.strokeStyle).toBe('#FF0000')
      expect(ctx.lineWidth).toBe(5)
    })

    it('should handle eraser tool correctly', () => {
      const point: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const settings: ToolSettings = { color: '#FFFFFF', size: 5, opacity: 1 }
      
      CanvasUtils.startDrawing(ctx as any, point, settings)
      
      expect(ctx.globalCompositeOperation).toBe('destination-out')
    })
  })

  describe('continueDrawing', () => {
    it('should draw line to point and stroke', () => {
      const point: DrawingPoint = { x: 30, y: 40, pressure: 1 }
      const settings: ToolSettings = { color: '#00FF00', size: 3, opacity: 1 }
      
      CanvasUtils.continueDrawing(ctx as any, point, settings)
      
      expect(ctx.lineTo).toHaveBeenCalledWith(30, 40)
      expect(ctx.stroke).toHaveBeenCalled()
      expect(ctx.beginPath).toHaveBeenCalled()
      expect(ctx.moveTo).toHaveBeenCalledWith(30, 40)
    })
  })

  describe('endDrawing', () => {
    it('should close the current path', () => {
      CanvasUtils.endDrawing(ctx as any)
      expect(ctx.closePath).toHaveBeenCalled()
    })
  })

  describe('eraseAt', () => {
    it('should erase at specific point with correct size', () => {
      const point: DrawingPoint = { x: 50, y: 60, pressure: 1 }
      const size = 10
      const originalCompositeOperation = ctx.globalCompositeOperation
      
      CanvasUtils.eraseAt(ctx as any, point, size)
      
      expect(ctx.beginPath).toHaveBeenCalled()
      expect(ctx.arc).toHaveBeenCalledWith(50, 60, 5, 0, Math.PI * 2)
      expect(ctx.fill).toHaveBeenCalled()
      // The composite operation should be restored after erasing
      expect(ctx.globalCompositeOperation).toBe(originalCompositeOperation)
    })

    it('should handle invalid parameters gracefully', () => {
      const invalidPoint: DrawingPoint = { x: NaN, y: NaN, pressure: 1 }
      const invalidSize = -5
      
      expect(() => {
        CanvasUtils.eraseAt(ctx as any, invalidPoint, invalidSize)
      }).not.toThrow()
      
      // Should not call canvas methods with invalid parameters
      expect(ctx.arc).not.toHaveBeenCalled()
    })
  })

  describe('eraseAlongPath', () => {
    it('should erase along path between two points', () => {
      const fromPoint: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const toPoint: DrawingPoint = { x: 30, y: 40, pressure: 1 }
      const size = 8
      
      CanvasUtils.eraseAlongPath(ctx as any, fromPoint, toPoint, size)
      
      expect(ctx.beginPath).toHaveBeenCalled()
      expect(ctx.moveTo).toHaveBeenCalledWith(10, 20)
      expect(ctx.lineTo).toHaveBeenCalledWith(30, 40)
      expect(ctx.stroke).toHaveBeenCalled()
    })

    it('should handle invalid path parameters gracefully', () => {
      const invalidFromPoint: DrawingPoint = { x: NaN, y: 10, pressure: 1 }
      const validToPoint: DrawingPoint = { x: 30, y: 40, pressure: 1 }
      const size = 8
      
      expect(() => {
        CanvasUtils.eraseAlongPath(ctx as any, invalidFromPoint, validToPoint, size)
      }).not.toThrow()
      
      // Should not call canvas methods with invalid parameters
      expect(ctx.moveTo).not.toHaveBeenCalled()
    })
  })

  describe('clearCanvas', () => {
    it('should clear canvas and fill with white background', () => {
      const bounds: CanvasBounds = { width: 800, height: 600, top: 0, left: 0 }
      
      CanvasUtils.clearCanvas(ctx as any, bounds)
      
      expect(ctx.clearRect).toHaveBeenCalledWith(0, 0, 800, 600)
      expect(ctx.fillStyle).toBe('#FFFFFF')
      expect(ctx.fillRect).toHaveBeenCalledWith(0, 0, 800, 600)
    })
  })

  describe('flipCanvas', () => {
    it('should flip canvas horizontally', () => {
      const bounds: CanvasBounds = { width: 800, height: 600, top: 0, left: 0 }
      
      CanvasUtils.flipCanvas(ctx as any, bounds)
      
      expect(ctx.getImageData).toHaveBeenCalledWith(0, 0, 800, 600)
      expect(ctx.clearRect).toHaveBeenCalledWith(0, 0, 800, 600)
      expect(ctx.save).toHaveBeenCalled()
      expect(ctx.scale).toHaveBeenCalledWith(-1, 1)
      expect(ctx.restore).toHaveBeenCalled()
    })
  })

  describe('screenToCanvasCoordinates', () => {
    it('should convert screen coordinates to canvas coordinates', () => {
      canvas.getBoundingClientRect = vi.fn(() => ({
        top: 10,
        left: 20,
        width: 200,
        height: 150
      }))
      canvas.width = 400
      canvas.height = 300
      
      const result = CanvasUtils.screenToCanvasCoordinates(120, 85, canvas as any)
      
      expect(result.x).toBe(200) // (120 - 20) * (400 / 200)
      expect(result.y).toBe(150) // (85 - 10) * (300 / 150)
    })
  })

  describe('isPointInBounds', () => {
    it('should return true for point within bounds', () => {
      const point: DrawingPoint = { x: 50, y: 60, pressure: 1 }
      const bounds: CanvasBounds = { width: 100, height: 100, top: 0, left: 0 }
      
      const result = CanvasUtils.isPointInBounds(point, bounds)
      expect(result).toBe(true)
    })

    it('should return false for point outside bounds', () => {
      const point: DrawingPoint = { x: 150, y: 60, pressure: 1 }
      const bounds: CanvasBounds = { width: 100, height: 100, top: 0, left: 0 }
      
      const result = CanvasUtils.isPointInBounds(point, bounds)
      expect(result).toBe(false)
    })
  })
})

describe('DrawingHistoryManager', () => {
  let historyManager: DrawingHistoryManager
  let mockImageData: MockImageData

  beforeEach(() => {
    historyManager = new DrawingHistoryManager(5) // Small history for testing
    mockImageData = new MockImageData(100, 100)
  })

  describe('addToHistory', () => {
    it('should add image data to history', () => {
      historyManager.addToHistory(mockImageData)
      
      expect(historyManager.getHistoryLength()).toBe(1)
      expect(historyManager.getCurrentIndex()).toBe(0)
    })

    it('should limit history size', () => {
      // Add more than max history size
      for (let i = 0; i < 7; i++) {
        historyManager.addToHistory(new MockImageData(100, 100) as any)
      }
      
      expect(historyManager.getHistoryLength()).toBe(5)
    })

    it('should remove redo history when adding new state', () => {
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      
      // Undo twice
      historyManager.undo()
      historyManager.undo()
      
      // Add new state - should remove redo history
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      
      expect(historyManager.canRedo()).toBe(false)
      expect(historyManager.getHistoryLength()).toBe(2)
    })
  })

  describe('undo and redo', () => {
    it('should undo and return previous state', () => {
      const imageData1 = new MockImageData(100, 100) as any
      const imageData2 = new MockImageData(100, 100) as any
      
      historyManager.addToHistory(imageData1)
      historyManager.addToHistory(imageData2)
      
      const result = historyManager.undo()
      expect(result).toBe(imageData1)
      expect(historyManager.getCurrentIndex()).toBe(0)
    })

    it('should redo and return next state', () => {
      const imageData1 = new MockImageData(100, 100) as any
      const imageData2 = new MockImageData(100, 100) as any
      
      historyManager.addToHistory(imageData1)
      historyManager.addToHistory(imageData2)
      historyManager.undo()
      
      const result = historyManager.redo()
      expect(result).toBe(imageData2)
      expect(historyManager.getCurrentIndex()).toBe(1)
    })

    it('should return null when cannot undo', () => {
      const result = historyManager.undo()
      expect(result).toBeNull()
    })

    it('should return null when cannot redo', () => {
      historyManager.addToHistory(mockImageData as any)
      const result = historyManager.redo()
      expect(result).toBeNull()
    })
  })

  describe('canUndo and canRedo', () => {
    it('should correctly report undo availability', () => {
      expect(historyManager.canUndo()).toBe(false)
      
      historyManager.addToHistory(mockImageData as any)
      expect(historyManager.canUndo()).toBe(false)
      
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      expect(historyManager.canUndo()).toBe(true)
    })

    it('should correctly report redo availability', () => {
      historyManager.addToHistory(mockImageData as any)
      historyManager.addToHistory(new MockImageData(100, 100) as any)
      
      expect(historyManager.canRedo()).toBe(false)
      
      historyManager.undo()
      expect(historyManager.canRedo()).toBe(true)
    })
  })

  describe('clearHistory', () => {
    it('should clear all history', () => {
      historyManager.addToHistory(mockImageData)
      historyManager.addToHistory(new ImageData(100, 100))
      
      historyManager.clearHistory()
      
      expect(historyManager.getHistoryLength()).toBe(0)
      expect(historyManager.getCurrentIndex()).toBe(-1)
    })
  })
})

describe('DrawingPathRecorder', () => {
  let pathRecorder: DrawingPathRecorder

  beforeEach(() => {
    pathRecorder = new DrawingPathRecorder()
  })

  describe('startPath', () => {
    it('should start recording a new path', () => {
      const point: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      
      pathRecorder.startPath(point)
      
      expect(pathRecorder.isCurrentlyRecording()).toBe(true)
      expect(pathRecorder.getCurrentPath()).toEqual([point])
    })
  })

  describe('addPoint', () => {
    it('should add point to current path', () => {
      const point1: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const point2: DrawingPoint = { x: 15, y: 25, pressure: 1 }
      
      pathRecorder.startPath(point1)
      pathRecorder.addPoint(point2)
      
      expect(pathRecorder.getCurrentPath()).toEqual([point1, point2])
    })

    it('should not add point if not recording', () => {
      const point: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      
      pathRecorder.addPoint(point)
      
      expect(pathRecorder.getCurrentPath()).toEqual([])
    })

    it('should filter out points too close to last point', () => {
      const point1: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const point2: DrawingPoint = { x: 10.5, y: 20.5, pressure: 1 } // Too close
      const point3: DrawingPoint = { x: 15, y: 25, pressure: 1 } // Far enough
      
      pathRecorder.startPath(point1)
      pathRecorder.addPoint(point2)
      pathRecorder.addPoint(point3)
      
      expect(pathRecorder.getCurrentPath()).toEqual([point1, point3])
    })
  })

  describe('endPath', () => {
    it('should end recording and return path', () => {
      const point1: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      const point2: DrawingPoint = { x: 15, y: 25, pressure: 1 }
      
      pathRecorder.startPath(point1)
      pathRecorder.addPoint(point2)
      
      const result = pathRecorder.endPath()
      
      expect(result).toEqual([point1, point2])
      expect(pathRecorder.isCurrentlyRecording()).toBe(false)
      expect(pathRecorder.getCurrentPath()).toEqual([])
    })
  })

  describe('clearCurrentPath', () => {
    it('should clear current path and stop recording', () => {
      const point: DrawingPoint = { x: 10, y: 20, pressure: 1 }
      
      pathRecorder.startPath(point)
      pathRecorder.clearCurrentPath()
      
      expect(pathRecorder.getCurrentPath()).toEqual([])
      expect(pathRecorder.isCurrentlyRecording()).toBe(false)
    })
  })
})