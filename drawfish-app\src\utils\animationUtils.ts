// Animation utility functions for fish swimming animations

import type { Position, Velocity, Dimensions, CanvasBounds } from '../types'

/**
 * Animation configuration constants
 */
export const ANIMATION_CONFIG = {
  TARGET_FPS: 60, // Higher frame rate for smoother swimming
  FRAME_TIME: 1000 / 60, // 16ms per frame
  TAIL_SPEED: 0.2, // Faster tail movement
  TAIL_AMPLITUDE: 10, // Larger tail swing
  DEFAULT_VELOCITY: { x: 3, y: 0.5 }, // Faster horizontal movement for more visible motion
  BOUNDARY_PADDING: 20
} as const

/**
 * Calculate new position based on current position, velocity, and delta time
 * @param currentPos Current position
 * @param velocity Current velocity
 * @param deltaTime Time elapsed since last frame (in milliseconds)
 * @returns New position
 */
export function calculatePosition(
  currentPos: Position,
  velocity: Velocity,
  deltaTime: number
): Position {
  // Normalize delta time to prevent large jumps during frame drops
  const normalizedDelta = Math.min(deltaTime, ANIMATION_CONFIG.FRAME_TIME * 2)
  const timeMultiplier = normalizedDelta / ANIMATION_CONFIG.FRAME_TIME
  
  return {
    x: currentPos.x + velocity.x * timeMultiplier,
    y: currentPos.y + velocity.y * timeMultiplier
  }
}

/**
 * Check boundaries and handle horizontal swimming with flipping
 * @param position Current position
 * @param velocity Current velocity
 * @param bounds Screen/container bounds
 * @param fishDimensions Fish dimensions
 * @returns Updated position, velocity, and flip indicator
 */
export function checkBoundaries(
  position: Position,
  velocity: Velocity,
  bounds: CanvasBounds,
  fishDimensions: Dimensions
): { position: Position; velocity: Velocity; shouldFlip: boolean } {
  let newPosition = { ...position }
  let newVelocity = { ...velocity }
  let shouldFlip = false
  
  const padding = ANIMATION_CONFIG.BOUNDARY_PADDING
  const fishWidth = fishDimensions.width
  const fishHeight = fishDimensions.height
  
  // Check horizontal boundaries - main swimming direction
  if (newPosition.x <= padding && newVelocity.x < 0) {
    // Hit left boundary while swimming left
    newPosition.x = padding
    newVelocity.x = Math.abs(newVelocity.x) // Reverse to swim right
    shouldFlip = true
  } else if (newPosition.x >= bounds.width - fishWidth - padding && newVelocity.x > 0) {
    // Hit right boundary while swimming right
    newPosition.x = bounds.width - fishWidth - padding
    newVelocity.x = -Math.abs(newVelocity.x) // Reverse to swim left
    shouldFlip = true
  }
  
  // Keep fish in vertical bounds but allow some gentle vertical movement
  const centerY = bounds.height / 2
  const maxVerticalOffset = bounds.height * 0.3 // Allow 30% vertical movement from center
  
  if (newPosition.y < centerY - maxVerticalOffset) {
    newPosition.y = centerY - maxVerticalOffset
    newVelocity.y = Math.abs(newVelocity.y) * 0.5 // Gentle bounce
  } else if (newPosition.y > centerY + maxVerticalOffset - fishHeight) {
    newPosition.y = centerY + maxVerticalOffset - fishHeight
    newVelocity.y = -Math.abs(newVelocity.y) * 0.5 // Gentle bounce
  }
  
  return { position: newPosition, velocity: newVelocity, shouldFlip }
}

/**
 * Update tail animation phase
 * @param currentPhase Current tail animation phase
 * @param speed Animation speed multiplier
 * @returns New tail phase
 */
export function updateTailAnimation(currentPhase: number, speed: number = 1): number {
  const newPhase = currentPhase + (ANIMATION_CONFIG.TAIL_SPEED * speed)
  // Keep phase within reasonable bounds to prevent overflow
  return newPhase % (Math.PI * 4)
}

/**
 * Calculate tail offset for swimming animation
 * @param phase Current animation phase
 * @param amplitude Tail swing amplitude
 * @returns Tail offset value
 */
export function calculateTailOffset(phase: number, amplitude: number = ANIMATION_CONFIG.TAIL_AMPLITUDE): number {
  return Math.sin(phase) * amplitude
}

/**
 * Create optimized animation loop with requestAnimationFrame
 * @param callback Animation callback function
 * @returns Animation control object
 */
export function createAnimationLoop(callback: (deltaTime: number) => void) {
  let animationId: number | null = null
  let lastTime = 0
  let isRunning = false
  
  const animate = (currentTime: number) => {
    if (!isRunning) return

    const deltaTime = currentTime - lastTime

    // Always call callback on first frame or when enough time has passed
    if (lastTime === 0 || deltaTime >= ANIMATION_CONFIG.FRAME_TIME - 1) {
      lastTime = currentTime
      callback(deltaTime)
    }

    animationId = requestAnimationFrame(animate)
  }
  
  return {
    start() {
      if (isRunning) return
      isRunning = true
      lastTime = 0 // Reset lastTime to ensure first frame is called
      animationId = requestAnimationFrame(animate)
    },
    
    stop() {
      isRunning = false
      if (animationId !== null) {
        cancelAnimationFrame(animationId)
        animationId = null
      }
    },
    
    isRunning() {
      return isRunning
    }
  }
}

/**
 * Generate random velocity within reasonable bounds
 * @param minSpeed Minimum speed
 * @param maxSpeed Maximum speed
 * @returns Random velocity
 */
export function generateRandomVelocity(minSpeed: number = 2, maxSpeed: number = 4): Velocity {
  // Generate primarily horizontal movement for fish swimming
  const horizontalSpeed = minSpeed + Math.random() * (maxSpeed - minSpeed)
  const direction = Math.random() > 0.5 ? 1 : -1 // Random left or right
  const verticalSpeed = (Math.random() - 0.5) * 1 // Small vertical component

  return {
    x: horizontalSpeed * direction,
    y: verticalSpeed
  }
}

/**
 * Interpolate between two positions for smooth movement
 * @param from Starting position
 * @param to Target position
 * @param factor Interpolation factor (0-1)
 * @returns Interpolated position
 */
export function interpolatePosition(from: Position, to: Position, factor: number): Position {
  const clampedFactor = Math.max(0, Math.min(1, factor))
  
  return {
    x: from.x + (to.x - from.x) * clampedFactor,
    y: from.y + (to.y - from.y) * clampedFactor
  }
}

/**
 * Calculate distance between two positions
 * @param pos1 First position
 * @param pos2 Second position
 * @returns Distance between positions
 */
export function calculateDistance(pos1: Position, pos2: Position): number {
  const dx = pos2.x - pos1.x
  const dy = pos2.y - pos1.y
  return Math.sqrt(dx * dx + dy * dy)
}