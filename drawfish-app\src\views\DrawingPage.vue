<template>
  <div class="drawing-page">
    <!-- Page Header -->
    <header class="page-header">
      <h1 class="page-title">🎨 Draw a Fish!</h1>
      <p class="page-subtitle">创造你独特的鱼类设计，然后观看它们游泳</p>
    </header>

    <!-- Main Content Area -->
    <main class="main-content">
      <!-- Toolbar Section -->
      <aside class="toolbar-section">
        <ToolBar
          :can-undo="canvasCanUndo"
          :can-redo="canvasCanRedo"
          :has-content="canvasHasContent"
          @tool-changed="handleToolChanged"
          @size-changed="handleSizeChanged"
          @undo="handleUndo"
          @redo="handleRedo"
          @clear="handleClear"
          @flip="handleFlip"
          @make-swim="handleMakeSwim"
        />
      </aside>

      <!-- Canvas Section -->
      <section class="canvas-section">
        <div class="canvas-wrapper">
          <DrawingCanvas
            ref="canvasRef"
            :width="canvasWidth"
            :height="canvasHeight"
            :show-preview="true"
            @canvas-ready="handleCanvasReady"
            @drawing-start="handleDrawingStart"
            @drawing-move="handleDrawingMove"
            @drawing-end="handleDrawingEnd"
            @canvas-cleared="handleCanvasCleared"
          />
        </div>
        
        <!-- Canvas Info -->
        <div class="canvas-info">
          <span class="canvas-size">{{ canvasWidth }} × {{ canvasHeight }}</span>
          <span class="drawing-status" :class="{ active: isDrawing }">
            {{ isDrawing ? '正在绘画...' : '准备绘画' }}
          </span>
        </div>
      </section>
    </main>

    <!-- Fish Naming Dialog -->
    <FishNamingDialog
      v-if="showNamingDialog"
      :fish-image="currentFishImage"
      @confirm="handleFishNamed"
      @cancel="handleNamingCanceled"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDrawingStore } from '@/stores/drawingStore'
import { useFishStore } from '@/stores/fishStore'
import ToolBar from '@/components/ToolBar.vue'
import DrawingCanvas from '@/components/DrawingCanvas.vue'
import FishNamingDialog from '@/components/FishNamingDialog.vue'
import type { DrawingTool, DrawingPoint } from '@/types'

// Router
const router = useRouter()

// Stores
const drawingStore = useDrawingStore()
const fishStore = useFishStore()

// Refs
const canvasRef = ref<InstanceType<typeof DrawingCanvas>>()

// State
const showNamingDialog = ref(false)
const currentFishImage = ref<ImageData | null>(null)

// Canvas dimensions - responsive
const canvasWidth = ref(800)
const canvasHeight = ref(600)

// Computed
const isDrawing = computed(() => drawingStore.isDrawing)

// Canvas state - these will be updated by canvas events
const canvasCanUndo = ref(false)
const canvasCanRedo = ref(false)
const canvasHasContent = ref(false)

// Handle responsive canvas sizing
const updateCanvasSize = () => {
  const container = document.querySelector('.canvas-section')
  if (container) {
    const containerRect = container.getBoundingClientRect()
    const maxWidth = Math.min(containerRect.width - 40, 1000) // 40px for padding
    const maxHeight = Math.min(window.innerHeight - 300, 700) // 300px for header and toolbar
    
    // Maintain aspect ratio
    const aspectRatio = 4 / 3
    let newWidth = maxWidth
    let newHeight = newWidth / aspectRatio
    
    if (newHeight > maxHeight) {
      newHeight = maxHeight
      newWidth = newHeight * aspectRatio
    }
    
    canvasWidth.value = Math.max(400, Math.floor(newWidth))
    canvasHeight.value = Math.max(300, Math.floor(newHeight))
  }
}

// Update canvas state
const updateCanvasState = () => {
  if (canvasRef.value) {
    canvasCanUndo.value = canvasRef.value.canUndo()
    canvasCanRedo.value = canvasRef.value.canRedo()
    // Check if canvas has content by getting image data and checking if it's not blank
    const imageData = canvasRef.value.getCanvasImageData()
    if (imageData) {
      // Check if any pixel is not transparent
      const data = imageData.data
      let hasNonTransparentPixel = false
      for (let i = 3; i < data.length; i += 4) {
        if (data[i] > 0) { // Alpha channel > 0
          hasNonTransparentPixel = true
          break
        }
      }
      canvasHasContent.value = hasNonTransparentPixel
    } else {
      canvasHasContent.value = false
    }
  }
}

// Event Handlers
const handleCanvasReady = (canvas: HTMLCanvasElement, context: CanvasRenderingContext2D) => {
  console.log('Canvas ready:', canvas.width, 'x', canvas.height)
  // Initial state update
  updateCanvasState()
}

const handleToolChanged = (tool: DrawingTool) => {
  console.log('Tool changed to:', tool)
}

const handleSizeChanged = (size: number) => {
  drawingStore.setBrushSize(size)
  console.log('Brush size changed to:', size)
}

const handleDrawingStart = (point: DrawingPoint) => {
  console.log('Drawing started at:', point)
}

const handleDrawingMove = (point: DrawingPoint) => {
  // Optional: Could show coordinates or other feedback
}

const handleDrawingEnd = (point: DrawingPoint) => {
  console.log('Drawing ended at:', point)
  // Update canvas state after drawing
  updateCanvasState()
}

const handleCanvasCleared = () => {
  console.log('Canvas cleared')
  // Update canvas state after clearing
  updateCanvasState()
}

const handleUndo = () => {
  if (canvasRef.value) {
    const success = canvasRef.value.undo()
    if (success) {
      console.log('Undo successful')
      updateCanvasState()
    }
  }
}

const handleRedo = () => {
  if (canvasRef.value) {
    const success = canvasRef.value.redo()
    if (success) {
      console.log('Redo successful')
      updateCanvasState()
    }
  }
}

const handleClear = () => {
  if (canvasRef.value) {
    canvasRef.value.clearCanvas()
    console.log('Canvas cleared')
    updateCanvasState()
  }
}

const handleFlip = () => {
  if (canvasRef.value) {
    canvasRef.value.flipCanvas()
    console.log('Canvas flipped')
    updateCanvasState()
  }
}

const handleMakeSwim = () => {
  if (!canvasRef.value) {
    console.error('Canvas not available')
    return
  }

  // Get the current canvas image data
  const imageData = canvasRef.value.getCanvasImageData()
  if (!imageData) {
    console.error('No image data available')
    return
  }

  // Check if canvas has content
  if (!canvasHasContent.value) {
    console.warn('No content to make swim')
    return
  }

  // Store the image data and show naming dialog
  currentFishImage.value = imageData
  showNamingDialog.value = true
}

const handleFishNamed = (name: string) => {
  if (!currentFishImage.value) {
    console.error('No fish image data available')
    return
  }

  try {
    // Get the strokes from drawing store
    const strokes = drawingStore.getStrokes()
    
    // Create the fish with the provided name and strokes
    const fish = fishStore.createFish(currentFishImage.value, name, strokes)
    
    // Store the fish in drawing store as well
    drawingStore.setLastDrawnFish(fish)
    
    // Close the dialog
    showNamingDialog.value = false
    currentFishImage.value = null
    
    console.log('Fish created:', fish.name)
    
    // Navigate to swimming page
    router.push({ name: 'Swimming', params: { fishId: fish.id } })
  } catch (error) {
    console.error('Failed to create fish:', error)
    // Could show error message to user
  }
}

const handleNamingCanceled = () => {
  showNamingDialog.value = false
  currentFishImage.value = null
  console.log('Fish naming canceled')
}

// Window resize handler
const handleResize = () => {
  updateCanvasSize()
}

// Lifecycle
onMounted(() => {
  updateCanvasSize()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.drawing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

/* Main Content */
.main-content {
  display: flex;
  gap: 30px;
  flex: 1;
  align-items: flex-start;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Toolbar Section */
.toolbar-section {
  flex-shrink: 0;
}

/* Canvas Section */
.canvas-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.canvas-wrapper {
  background: white;
  border-radius: 12px;
  padding: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

.canvas-wrapper:hover {
  transform: translateY(-2px);
}

/* Canvas Info */
.canvas-info {
  display: flex;
  gap: 20px;
  align-items: center;
  color: white;
  font-size: 0.9rem;
  opacity: 0.8;
}

.canvas-size {
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
  font-family: monospace;
}

.drawing-status {
  padding: 4px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.drawing-status.active {
  background: rgba(76, 175, 80, 0.8);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .toolbar-section {
    width: 100%;
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .drawing-page {
    padding: 15px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .page-subtitle {
    font-size: 1rem;
  }
  
  .main-content {
    gap: 15px;
  }
  
  .canvas-wrapper {
    padding: 8px;
  }
  
  .canvas-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .drawing-page {
    padding: 10px;
  }
  
  .page-header {
    margin-bottom: 20px;
  }
  
  .page-title {
    font-size: 1.8rem;
  }
  
  .canvas-wrapper {
    padding: 5px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .canvas-wrapper {
    transition: none;
  }
  
  .drawing-status.active {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .drawing-page {
    background: #000;
    color: #fff;
  }
  
  .canvas-wrapper {
    border: 2px solid #fff;
  }
}
</style>