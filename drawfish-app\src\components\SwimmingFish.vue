<template>
    <div ref="fishContainer" class="swimming-fish" :style="fishStyle" @click="onFishClick">
        <!-- Fish image -->
        <canvas ref="fishCanvas" class="fish-canvas" :width="fishData.dimensions.width"
            :height="fishData.dimensions.height" />

        <!-- Fish name display -->
        <div v-if="showName && fishData.name" class="fish-name" :class="{ 'name-visible': nameVisible }">
            {{ fishData.name }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import type { FishData, CanvasBounds } from '../types'
import { FishAnimator } from '../utils/fishAnimator'

interface Props {
    fishData: FishData
    bounds: CanvasBounds
    autoStart?: boolean
    showName?: boolean
    interactive?: boolean
}

interface Emits {
    (e: 'update:fishData', fishData: FishData): void
    (e: 'fishClick', fishData: FishData): void
    (e: 'animationStart'): void
    (e: 'animationStop'): void
}

const props = withDefaults(defineProps<Props>(), {
    autoStart: true,
    showName: true,
    interactive: false
})

const emit = defineEmits<Emits>()

// Template refs
const fishContainer = ref<HTMLDivElement>()
const fishCanvas = ref<HTMLCanvasElement>()

// Component state
const animator = ref<FishAnimator>()
const nameVisible = ref(false)
const isAnimating = ref(false)

// Computed styles for fish positioning and animation
const fishStyle = computed(() => {
    const tailOffset = animator.value?.getTailOffset() || 0
    const direction = props.fishData.animationState.direction

    return {
        position: 'absolute' as const,
        left: `${props.fishData.position.x}px`,
        top: `${props.fishData.position.y}px`,
        transform: `scaleX(${direction}) skewX(${tailOffset * 0.1}deg)`,
        transition: isAnimating.value ? 'none' : 'transform 0.3s ease',
        cursor: props.interactive ? 'pointer' : 'default',
        zIndex: 10
    }
})

// Initialize fish canvas with image data
const initializeFishCanvas = async (): Promise<void> => {
    if (!fishCanvas.value) return

    const canvas = fishCanvas.value
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    try {
        if (typeof props.fishData.imageData === 'string') {
            // Handle base64 image data
            const img = new Image()
            img.onload = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height)
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
            }
            img.src = props.fishData.imageData
        } else {
            // Handle ImageData
            ctx.putImageData(props.fishData.imageData, 0, 0)
        }
    } catch (error) {
        console.error('Failed to load fish image:', error)
        // Draw a simple placeholder fish shape
        drawPlaceholderFish(ctx)
    }
}

// Draw a simple placeholder fish when image fails to load
const drawPlaceholderFish = (ctx: CanvasRenderingContext2D): void => {
    const { width, height } = props.fishData.dimensions

    ctx.clearRect(0, 0, width, height)

    // Fish body (ellipse)
    ctx.fillStyle = '#4A90E2'
    ctx.beginPath()
    ctx.ellipse(width * 0.4, height * 0.5, width * 0.3, height * 0.3, 0, 0, Math.PI * 2)
    ctx.fill()

    // Fish tail
    ctx.beginPath()
    ctx.moveTo(width * 0.1, height * 0.5)
    ctx.lineTo(0, height * 0.3)
    ctx.lineTo(0, height * 0.7)
    ctx.closePath()
    ctx.fill()

    // Fish eye
    ctx.fillStyle = '#FFFFFF'
    ctx.beginPath()
    ctx.arc(width * 0.5, height * 0.4, width * 0.08, 0, Math.PI * 2)
    ctx.fill()

    ctx.fillStyle = '#000000'
    ctx.beginPath()
    ctx.arc(width * 0.52, height * 0.38, width * 0.04, 0, Math.PI * 2)
    ctx.fill()
}

// Handle fish click
const onFishClick = (): void => {
    if (props.interactive) {
        emit('fishClick', props.fishData)

        // Show name temporarily on click
        if (props.showName && props.fishData.name) {
            showNameTemporarily()
        }
    }
}

// Show fish name temporarily
const showNameTemporarily = (): void => {
    nameVisible.value = true
    setTimeout(() => {
        nameVisible.value = false
    }, 2000)
}

// Start animation
const startAnimation = (): void => {
    if (animator.value && !isAnimating.value) {
        animator.value.startAnimation()
        isAnimating.value = true
        emit('animationStart')
    }
}

// Stop animation
const stopAnimation = (): void => {
    if (animator.value && isAnimating.value) {
        animator.value.stopAnimation()
        isAnimating.value = false
        emit('animationStop')
    }
}

// Pause animation
const pauseAnimation = (): void => {
    if (animator.value) {
        animator.value.pause()
        isAnimating.value = false
    }
}

// Resume animation
const resumeAnimation = (): void => {
    if (animator.value) {
        animator.value.resume()
        isAnimating.value = true
    }
}

// Update bounds (for window resize)
const updateBounds = (newBounds: CanvasBounds): void => {
    if (animator.value) {
        animator.value.updateBounds(newBounds)
    }
}

// Watch for bounds changes
watch(() => props.bounds, (newBounds) => {
    updateBounds(newBounds)
}, { deep: true })

// Watch for fish data changes
watch(() => props.fishData, async (newFishData) => {
    if (animator.value) {
        // Update animator with new fish data
        animator.value.reset(newFishData)
    }

    // Reinitialize canvas with new image data
    await nextTick()
    await initializeFishCanvas()
}, { deep: true })

// Component lifecycle
onMounted(async () => {
    await nextTick()
    await initializeFishCanvas()

    // Create animator
    animator.value = new FishAnimator(
        props.fishData,
        props.bounds,
        (updatedFishData) => {
            emit('update:fishData', updatedFishData)
        }
    )

    // Auto-start animation if enabled
    if (props.autoStart) {
        startAnimation()
    }

    // Show name initially if enabled
    if (props.showName && props.fishData.name) {
        setTimeout(() => {
            showNameTemporarily()
        }, 500)
    }
})

onUnmounted(() => {
    if (animator.value) {
        animator.value.destroy()
    }
})

// Expose methods for parent components
defineExpose({
    startAnimation,
    stopAnimation,
    pauseAnimation,
    resumeAnimation,
    updateBounds,
    isAnimating: () => isAnimating.value,
    getAnimator: () => animator.value
})
</script>

<style scoped>
.swimming-fish {
    pointer-events: auto;
    user-select: none;
}

.fish-canvas {
    display: block;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.fish-name {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 20;
}

.fish-name.name-visible {
    opacity: 1;
}

.swimming-fish:hover .fish-name {
    opacity: 1;
}

/* Animation effects */
.swimming-fish {
    will-change: transform;
}

/* Interactive fish effects */
.swimming-fish[style*="cursor: pointer"]:hover {
    filter: brightness(1.1);
}

.swimming-fish[style*="cursor: pointer"]:active {
    transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .fish-name {
        font-size: 10px;
        padding: 2px 6px;
        top: -25px;
    }
}
</style>