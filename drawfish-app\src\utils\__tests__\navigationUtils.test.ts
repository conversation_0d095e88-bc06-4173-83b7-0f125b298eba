import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock ImageData for test environment
global.ImageData = class ImageData {
  width: number
  height: number
  data: Uint8ClampedArray

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
}
import { createRouter, createWebHistory } from 'vue-router'
import type { Router } from 'vue-router'
import { setActivePinia, createPinia } from 'pinia'
import { useFishStore } from '@/stores/fishStore'
import type { FishData } from '@/types'
import {
  navigateToDrawing,
  navigateToSwimming,
  navigateToSwimmingWithFish,
  canNavigateToSwimming,
  getCurrentRouteName,
  isCurrentRoute,
  navigateBack,
  createFishAndNavigate,
  validateSwimmingPageAccess,
  getNavigationBreadcrumbs,
  NavigationState,
  routeTransitions,
  generateSwimmingUrl,
  generateDrawingUrl,
  getRouteTitle,
  getRouteDescription
} from '../navigationUtils'

// Mock router setup
function createTestRouter(): Router {
  return createRouter({
    history: createWebHistory(),
    routes: [
      {
        path: '/',
        name: 'Drawing',
        component: { name: 'DrawingPage' },
        meta: { 
          title: 'Draw a Fish!',
          description: '创造你独特的鱼类设计'
        }
      },
      {
        path: '/swimming/:fishId?',
        name: 'Swimming',
        component: { name: 'SwimmingPage' },
        meta: { 
          title: 'Watch Your Fish Swim!',
          description: '观看你的鱼在水中游泳'
        }
      }
    ],
  })
}

describe('Navigation Utils', () => {
  let router: Router
  let fishStore: ReturnType<typeof useFishStore>

  const mockFish: FishData = {
    id: 'test-fish-123',
    name: 'Test Fish',
    imageData: 'mock-image-data',
    createdAt: new Date(),
    dimensions: { width: 400, height: 300 },
    position: { x: 100, y: 100 },
    velocity: { x: 2, y: 1 },
    animationState: { tailPhase: 0, direction: 1 }
  }

  beforeEach(() => {
    setActivePinia(createPinia())
    router = createTestRouter()
    fishStore = useFishStore()
    fishStore.clearCollection()
    fishStore.setCurrentFish(null)
  })

  describe('Basic Navigation Functions', () => {
    it('should navigate to drawing page', async () => {
      await navigateToDrawing(router)
      expect(router.currentRoute.value.name).toBe('Drawing')
      expect(router.currentRoute.value.path).toBe('/')
    })

    it('should navigate to swimming page without fish ID', async () => {
      fishStore.setCurrentFish(mockFish)
      await navigateToSwimming(router)
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.path).toBe('/swimming')
    })

    it('should navigate to swimming page with fish ID', async () => {
      fishStore.addToCollection(mockFish)
      await navigateToSwimming(router, mockFish.id)
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.path).toBe(`/swimming/${mockFish.id}`)
      expect(router.currentRoute.value.params.fishId).toBe(mockFish.id)
    })

    it('should navigate to swimming page with specific fish', async () => {
      await navigateToSwimmingWithFish(router, mockFish)
      
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.params.fishId).toBe(mockFish.id)
      expect(fishStore.currentFish?.id).toBe(mockFish.id)
      expect(fishStore.fishCollection.some(f => f.id === mockFish.id)).toBe(true)
    })
  })

  describe('Navigation Validation', () => {
    it('should allow navigation to swimming when fish is available', () => {
      fishStore.setCurrentFish(mockFish)
      expect(canNavigateToSwimming()).toBe(true)
    })

    it('should not allow navigation to swimming when no fish is available', () => {
      expect(canNavigateToSwimming()).toBe(false)
    })

    it('should allow navigation to swimming with valid fish ID', () => {
      fishStore.addToCollection(mockFish)
      expect(canNavigateToSwimming(mockFish.id)).toBe(true)
    })

    it('should not allow navigation to swimming with invalid fish ID', () => {
      expect(canNavigateToSwimming('invalid-fish-id')).toBe(false)
    })

    it('should validate swimming page access correctly', () => {
      // No fish available
      let result = validateSwimmingPageAccess()
      expect(result.canAccess).toBe(false)
      expect(result.redirectTo).toBe('Drawing')
      expect(result.reason).toContain('No fish available')

      // Valid fish available
      fishStore.setCurrentFish(mockFish)
      result = validateSwimmingPageAccess()
      expect(result.canAccess).toBe(true)
      expect(result.redirectTo).toBeUndefined()

      // Valid fish ID
      fishStore.addToCollection(mockFish)
      result = validateSwimmingPageAccess(mockFish.id)
      expect(result.canAccess).toBe(true)

      // Invalid fish ID
      result = validateSwimmingPageAccess('invalid-id')
      expect(result.canAccess).toBe(false)
      expect(result.redirectTo).toBe('Drawing')
      expect(result.reason).toContain('not found')
    })
  })

  describe('Route Information', () => {
    it('should get current route name', async () => {
      await router.push('/')
      expect(getCurrentRouteName(router)).toBe('Drawing')

      fishStore.setCurrentFish(mockFish)
      await router.push('/swimming')
      expect(getCurrentRouteName(router)).toBe('Swimming')
    })

    it('should check if on current route', async () => {
      await router.push('/')
      expect(isCurrentRoute(router, 'Drawing')).toBe(true)
      expect(isCurrentRoute(router, 'Swimming')).toBe(false)

      fishStore.setCurrentFish(mockFish)
      await router.push('/swimming')
      expect(isCurrentRoute(router, 'Swimming')).toBe(true)
      expect(isCurrentRoute(router, 'Drawing')).toBe(false)
    })

    it('should get route title and description', async () => {
      await router.push('/')
      expect(getRouteTitle(router)).toBe('Draw a Fish!')
      expect(getRouteDescription(router)).toBe('创造你独特的鱼类设计')

      fishStore.setCurrentFish(mockFish)
      await router.push('/swimming')
      expect(getRouteTitle(router)).toBe('Watch Your Fish Swim!')
      expect(getRouteDescription(router)).toBe('观看你的鱼在水中游泳')
    })
  })

  describe('Navigation Breadcrumbs', () => {
    it('should generate breadcrumbs for drawing page', async () => {
      await router.push('/')
      const breadcrumbs = getNavigationBreadcrumbs(router)
      
      expect(breadcrumbs).toHaveLength(1)
      expect(breadcrumbs[0]).toEqual({
        name: 'Drawing',
        title: '绘画页面',
        path: '/',
        current: true
      })
    })

    it('should generate breadcrumbs for swimming page', async () => {
      fishStore.setCurrentFish(mockFish)
      await router.push('/swimming')
      const breadcrumbs = getNavigationBreadcrumbs(router)
      
      expect(breadcrumbs).toHaveLength(2)
      expect(breadcrumbs[0]).toEqual({
        name: 'Drawing',
        title: '绘画页面',
        path: '/',
        current: false
      })
      expect(breadcrumbs[1]).toEqual({
        name: 'Swimming',
        title: mockFish.name,
        path: '/swimming',
        current: true
      })
    })

    it('should handle swimming page breadcrumbs without fish name', async () => {
      await router.push('/swimming')
      const breadcrumbs = getNavigationBreadcrumbs(router)
      
      expect(breadcrumbs).toHaveLength(2)
      expect(breadcrumbs[1].title).toBe('游泳页面')
    })
  })

  describe('Fish Creation and Navigation', () => {
    it('should create fish and navigate to swimming page', async () => {
      const imageData = 'test-image-data'
      const fishName = 'Created Fish'
      
      const createdFish = await createFishAndNavigate(router, imageData, fishName)
      
      expect(createdFish.name).toBe(fishName)
      expect(createdFish.imageData).toBe(imageData)
      expect(router.currentRoute.value.name).toBe('Swimming')
      expect(router.currentRoute.value.params.fishId).toBe(createdFish.id)
      expect(fishStore.currentFish?.id).toBe(createdFish.id)
    })

    it('should handle errors during fish creation', async () => {
      // Mock fishStore.createFish to throw an error
      const originalCreateFish = fishStore.createFish
      fishStore.createFish = vi.fn().mockImplementation(() => {
        throw new Error('Creation failed')
      })

      await expect(createFishAndNavigate(router, 'data', 'name')).rejects.toThrow('Creation failed')
      
      // Restore original function
      fishStore.createFish = originalCreateFish
    })
  })

  describe('Navigation State', () => {
    it('should track navigation history', async () => {
      const navState = new NavigationState(router)
      
      await router.push('/')
      await router.push('/swimming')
      
      const history = navState.getNavigationHistory()
      expect(history).toContain('/')
      expect(history).toContain('/swimming')
    })

    it('should get previous route', async () => {
      const navState = new NavigationState(router)
      
      await router.push('/')
      await router.push('/swimming')
      
      expect(navState.getPreviousRoute()).toBe('/')
    })

    it('should clear navigation history', async () => {
      const navState = new NavigationState(router)
      
      await router.push('/')
      await router.push('/swimming')
      
      navState.clearHistory()
      expect(navState.getNavigationHistory()).toHaveLength(0)
    })

    it('should limit navigation history size', async () => {
      const navState = new NavigationState(router)
      
      // Navigate to many routes
      for (let i = 0; i < 15; i++) {
        await router.push(`/?test=${i}`)
      }
      
      const history = navState.getNavigationHistory()
      expect(history.length).toBeLessThanOrEqual(10)
    })
  })

  describe('URL Generation', () => {
    it('should generate swimming URL with fish ID', () => {
      const url = generateSwimmingUrl('test-fish-123')
      expect(url).toBe('/swimming/test-fish-123')
    })

    it('should generate drawing URL', () => {
      const url = generateDrawingUrl()
      expect(url).toBe('/')
    })
  })

  describe('Route Transitions', () => {
    it('should provide fade transition configuration', () => {
      expect(routeTransitions.fade).toEqual({
        name: 'fade',
        mode: 'out-in'
      })
    })

    it('should provide slide transition configuration', () => {
      expect(routeTransitions.slide).toEqual({
        name: 'slide',
        mode: 'out-in'
      })
    })
  })

  describe('Navigate Back', () => {
    it('should navigate back when history is available', async () => {
      // Mock window.history
      const originalHistory = window.history
      Object.defineProperty(window, 'history', {
        value: { length: 2 },
        writable: true
      })

      const routerBackSpy = vi.spyOn(router, 'back').mockImplementation(() => {})
      
      await navigateBack(router)
      expect(routerBackSpy).toHaveBeenCalled()
      
      // Restore
      Object.defineProperty(window, 'history', {
        value: originalHistory,
        writable: true
      })
      routerBackSpy.mockRestore()
    })

    it('should navigate to drawing page when no history is available', async () => {
      // Mock window.history
      const originalHistory = window.history
      Object.defineProperty(window, 'history', {
        value: { length: 1 },
        writable: true
      })

      await navigateBack(router)
      expect(router.currentRoute.value.name).toBe('Drawing')
      
      // Restore
      Object.defineProperty(window, 'history', {
        value: originalHistory,
        writable: true
      })
    })
  })
})