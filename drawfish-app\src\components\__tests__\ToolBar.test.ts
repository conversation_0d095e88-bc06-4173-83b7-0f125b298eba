import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import ToolBar from '../ToolBar.vue'
import { useDrawingStore } from '@/stores/drawingStore'

// Mock ImageData for testing environment
global.ImageData = class ImageData {
  data: Uint8ClampedArray
  width: number
  height: number
  
  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
} as any

// Mock the child components
vi.mock('../ColorPicker.vue', () => ({
  default: {
    name: 'ColorPicker',
    template: '<div class="color-picker-mock">ColorPicker</div>'
  }
}))

vi.mock('../SizeSlider.vue', () => ({
  default: {
    name: 'SizeSlider',
    template: '<div class="size-slider-mock">SizeSlider</div>',
    props: ['previewColor'],
    emits: ['size-changed']
  }
}))

describe('ToolBar', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders correctly with all sections', () => {
    const wrapper = mount(ToolBar)
    
    // Check if main toolbar renders
    expect(wrapper.find('.toolbar').exists()).toBe(true)
    expect(wrapper.find('.toolbar-title').text()).toBe('绘画工具')
    
    // Check if all sections exist
    expect(wrapper.find('.tool-buttons').exists()).toBe(true)
    expect(wrapper.find('.color-picker-mock').exists()).toBe(true)
    expect(wrapper.find('.size-slider-mock').exists()).toBe(true)
    expect(wrapper.find('.action-buttons').exists()).toBe(true)
    expect(wrapper.find('.swim-button').exists()).toBe(true)
  })

  it('displays tool buttons correctly', () => {
    const wrapper = mount(ToolBar)
    
    const toolButtons = wrapper.findAll('.tool-button')
    expect(toolButtons.length).toBe(2)
    
    // Check brush button
    const brushButton = toolButtons.find(button => 
      button.text().includes('画笔')
    )
    expect(brushButton).toBeDefined()
    
    // Check eraser button
    const eraserButton = toolButtons.find(button => 
      button.text().includes('擦除')
    )
    expect(eraserButton).toBeDefined()
  })

  it('highlights active tool button', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Default tool should be brush
    const toolButtons = wrapper.findAll('.tool-button')
    const brushButton = toolButtons.find(button => 
      button.text().includes('画笔')
    )
    expect(brushButton?.classes()).toContain('active')
    
    // Change to eraser
    store.setTool('eraser')
    await wrapper.vm.$nextTick()
    
    const eraserButton = toolButtons.find(button => 
      button.text().includes('擦除')
    )
    expect(eraserButton?.classes()).toContain('active')
    expect(brushButton?.classes()).not.toContain('active')
  })

  it('calls setTool and emits tool-changed when tool button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    const setToolSpy = vi.spyOn(store, 'setTool')
    
    const toolButtons = wrapper.findAll('.tool-button')
    const eraserButton = toolButtons.find(button => 
      button.text().includes('擦除')
    )
    
    await eraserButton?.trigger('click')
    
    expect(setToolSpy).toHaveBeenCalledWith('eraser')
    expect(wrapper.emitted('tool-changed')).toBeTruthy()
    expect(wrapper.emitted('tool-changed')?.[0]).toEqual(['eraser'])
  })

  it('displays action buttons correctly', () => {
    const wrapper = mount(ToolBar)
    
    const actionButtons = wrapper.findAll('.action-button')
    expect(actionButtons.length).toBe(4)
    
    // Check all action buttons exist
    const buttonTexts = actionButtons.map(button => button.text())
    expect(buttonTexts).toContain('↶撤销')
    expect(buttonTexts).toContain('↷重做')
    expect(buttonTexts).toContain('🗑️清除')
    expect(buttonTexts).toContain('↔️翻转')
  })

  it('disables action buttons when appropriate', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Initially, undo/redo/clear/flip should be disabled (no content)
    const undoButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('撤销')
    )
    const clearButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('清除')
    )
    
    expect(undoButton?.classes()).toContain('disabled')
    expect(undoButton?.attributes('disabled')).toBeDefined()
    expect(clearButton?.classes()).toContain('disabled')
    expect(clearButton?.attributes('disabled')).toBeDefined()
  })

  it('calls undo and emits undo event when undo button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add some history first
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    store.addToHistory(mockImageData)
    
    const undoSpy = vi.spyOn(store, 'undo')
    
    await wrapper.vm.$nextTick()
    
    const undoButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('撤销')
    )
    
    await undoButton?.trigger('click')
    
    expect(undoSpy).toHaveBeenCalled()
    expect(wrapper.emitted('undo')).toBeTruthy()
  })

  it('calls redo and emits redo event when redo button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add history and undo to enable redo
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    store.addToHistory(mockImageData)
    store.undo() // This enables redo
    
    const redoSpy = vi.spyOn(store, 'redo')
    
    await wrapper.vm.$nextTick()
    
    const redoButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('重做')
    )
    
    await redoButton?.trigger('click')
    
    expect(redoSpy).toHaveBeenCalled()
    expect(wrapper.emitted('redo')).toBeTruthy()
  })

  it('calls clearCanvas and emits clear event when clear button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add content to enable clear button
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    
    const clearSpy = vi.spyOn(store, 'clearCanvas')
    
    await wrapper.vm.$nextTick()
    
    const clearButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('清除')
    )
    
    await clearButton?.trigger('click')
    
    expect(clearSpy).toHaveBeenCalled()
    expect(wrapper.emitted('clear')).toBeTruthy()
  })

  it('calls flipCanvas and emits flip event when flip button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add content to enable flip button
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    
    const flipSpy = vi.spyOn(store, 'flipCanvas')
    
    await wrapper.vm.$nextTick()
    
    const flipButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('翻转')
    )
    
    await flipButton?.trigger('click')
    
    expect(flipSpy).toHaveBeenCalled()
    expect(wrapper.emitted('flip')).toBeTruthy()
  })

  it('displays swim button correctly', () => {
    const wrapper = mount(ToolBar)
    
    const swimButton = wrapper.find('.swim-button')
    expect(swimButton.exists()).toBe(true)
    expect(swimButton.text()).toContain('Make it Swim!')
  })

  it('disables swim button when no content', () => {
    const wrapper = mount(ToolBar)
    
    const swimButton = wrapper.find('.swim-button')
    expect(swimButton.classes()).toContain('disabled')
    expect(swimButton.attributes('disabled')).toBeDefined()
  })

  it('enables swim button when has content', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add content to enable swim button
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    
    await wrapper.vm.$nextTick()
    
    const swimButton = wrapper.find('.swim-button')
    expect(swimButton.classes()).not.toContain('disabled')
    expect(swimButton.attributes('disabled')).toBeUndefined()
  })

  it('emits make-swim event when swim button is clicked', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Add content to enable swim button
    const mockImageData = new ImageData(100, 100)
    store.addToHistory(mockImageData)
    
    await wrapper.vm.$nextTick()
    
    const swimButton = wrapper.find('.swim-button')
    await swimButton.trigger('click')
    
    expect(wrapper.emitted('make-swim')).toBeTruthy()
  })

  it('passes correct preview color to SizeSlider', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    // Default tool is brush, should use current color
    let sizeSlider = wrapper.findComponent({ name: 'SizeSlider' })
    expect(sizeSlider.props('previewColor')).toBe('#000000') // default color
    
    // Change to eraser, should use white
    store.setTool('eraser')
    await wrapper.vm.$nextTick()
    
    sizeSlider = wrapper.findComponent({ name: 'SizeSlider' })
    expect(sizeSlider.props('previewColor')).toBe('#FFFFFF')
  })

  it('emits size-changed when SizeSlider emits size-changed', async () => {
    const wrapper = mount(ToolBar)
    
    const sizeSlider = wrapper.findComponent({ name: 'SizeSlider' })
    await sizeSlider.vm.$emit('size-changed', 15)
    
    expect(wrapper.emitted('size-changed')).toBeTruthy()
    expect(wrapper.emitted('size-changed')?.[0]).toEqual([15])
  })

  it('has proper accessibility attributes', () => {
    const wrapper = mount(ToolBar)
    
    // Check tool buttons have aria-labels
    const toolButtons = wrapper.findAll('.tool-button')
    toolButtons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
      expect(button.attributes('title')).toBeDefined()
    })
    
    // Check action buttons have aria-labels
    const actionButtons = wrapper.findAll('.action-button')
    actionButtons.forEach(button => {
      expect(button.attributes('aria-label')).toBeDefined()
      expect(button.attributes('title')).toBeDefined()
    })
    
    // Check swim button has aria-label
    const swimButton = wrapper.find('.swim-button')
    expect(swimButton.attributes('aria-label')).toBeDefined()
    expect(swimButton.attributes('title')).toBeDefined()
  })

  it('does not call actions when buttons are disabled', async () => {
    const wrapper = mount(ToolBar)
    const store = useDrawingStore()
    
    const undoSpy = vi.spyOn(store, 'undo')
    const clearSpy = vi.spyOn(store, 'clearCanvas')
    
    // Try to click disabled buttons
    const undoButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('撤销')
    )
    const clearButton = wrapper.findAll('.action-button').find(button => 
      button.text().includes('清除')
    )
    const swimButton = wrapper.find('.swim-button')
    
    await undoButton?.trigger('click')
    await clearButton?.trigger('click')
    await swimButton.trigger('click')
    
    // Actions should not be called when disabled
    expect(undoSpy).not.toHaveBeenCalled()
    expect(clearSpy).not.toHaveBeenCalled()
    expect(wrapper.emitted('make-swim')).toBeFalsy()
  })
})