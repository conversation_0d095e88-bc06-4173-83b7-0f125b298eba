// Component props and emits type definitions

import type { FishData, DrawingTool, Position, Dimensions } from './index'

/**
 * DrawingCanvas component props
 */
export interface DrawingCanvasProps {
  width: number
  height: number
  currentTool: DrawingTool
  currentColor: string
  brushSize: number
  disabled?: boolean
}

/**
 * DrawingCanvas component emits
 */
export interface DrawingCanvasEmits {
  'canvas-updated': [imageData: ImageData]
  'drawing-complete': [fishData: FishData]
  'drawing-start': []
  'drawing-end': []
}

/**
 * ToolBar component props
 */
export interface ToolBarProps {
  currentTool: DrawingTool
  currentColor: string
  brushSize: number
  canUndo: boolean
  canRedo: boolean
  hasContent: boolean
}

/**
 * ToolBar component emits
 */
export interface ToolBarEmits {
  'tool-change': [tool: DrawingTool]
  'color-change': [color: string]
  'size-change': [size: number]
  'undo': []
  'redo': []
  'clear': []
  'flip': []
  'make-swim': []
}

/**
 * ColorPicker component props
 */
export interface ColorPickerProps {
  currentColor: string
  colors?: string[]
}

/**
 * ColorPicker component emits
 */
export interface ColorPickerEmits {
  'color-change': [color: string]
}

/**
 * SizeSlider component props
 */
export interface SizeSliderProps {
  currentSize: number
  minSize?: number
  maxSize?: number
  step?: number
}

/**
 * SizeSlider component emits
 */
export interface SizeSliderEmits {
  'size-change': [size: number]
}

/**
 * FishNamingDialog component props
 */
export interface FishNamingDialogProps {
  visible: boolean
  fishPreview?: string | ImageData
}

/**
 * FishNamingDialog component emits
 */
export interface FishNamingDialogEmits {
  'confirm': [name: string]
  'cancel': []
  'close': []
}

/**
 * SwimmingFish component props
 */
export interface SwimmingFishProps {
  fish: FishData
  bounds: Dimensions
  animationSpeed?: number
}

/**
 * SwimmingFish component emits
 */
export interface SwimmingFishEmits {
  'position-update': [position: Position]
  'animation-complete': []
}

/**
 * FishTank component props
 */
export interface FishTankProps {
  width: number
  height: number
  theme?: 'default' | 'ocean' | 'tropical'
}

/**
 * FishTank component emits
 */
export interface FishTankEmits {
  'bounds-change': [bounds: Dimensions]
}