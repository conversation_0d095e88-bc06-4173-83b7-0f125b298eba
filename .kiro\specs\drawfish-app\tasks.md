# 实施计划

- [x] 1. 项目初始化和基础配置





  - 创建Vue3+Vite项目结构
  - 配置Vue Router和Pinia
  - 安装必要依赖（axios等）
  - 设置基础的项目目录结构
  - _需求: 5.1, 5.3_

- [x] 2. 创建核心数据模型和类型定义





  - 定义FishData接口和相关类型
  - 创建DrawingTool类型定义
  - 实现基础的工具函数接口
  - _需求: 1.2, 2.5, 3.3_

- [x] 3. 实现Pinia状态管理





- [x] 3.1 创建绘画状态管理store



  - 实现drawingStore的状态定义
  - 添加工具切换、颜色设置、画笔大小等actions
  - 实现画布历史记录管理
  - 编写store的单元测试
  - _需求: 1.2, 1.3, 1.4, 1.6_


- [x] 3.2 创建鱼类数据管理store





  - 实现fishStore的状态定义
  - 添加鱼类创建、保存、管理等actions
  - 实现鱼类数据的持久化逻辑
  - 编写store的单元测试
  - _需求: 3.3, 4.4, 5.3_

- [x] 4. 开发绘画工具栏组件




- [x] 4.1 创建颜色选择器组件


  - 实现ColorPicker.vue组件
  - 添加预设颜色按钮和当前颜色显示
  - 集成到drawingStore状态管理
  - 编写组件测试
  - _需求: 1.1, 1.2_

- [x] 4.2 创建线条粗细控制组件


  - 实现SizeSlider.vue组件
  - 添加滑块控制和数值显示
  - 集成到drawingStore状态管理
  - 编写组件测试
  - _需求: 1.4_

- [x] 4.3 创建主工具栏组件



  - 实现ToolBar.vue组件
  - 集成颜色选择器和线条粗细控制
  - 添加擦除、撤销、清除、翻转按钮
  - 实现工具切换逻辑
  - 编写组件测试
  - _需求: 1.3, 1.5, 1.6, 1.7_

- [x] 5. 开发Canvas绘画功能





- [x] 5.1 创建Canvas工具函数



  - 实现canvasUtils.js工具函数库
  - 添加Canvas初始化、绘画路径记录等函数
  - 实现撤销/重做的历史管理
  - 编写工具函数的单元测试
  - _需求: 2.1, 2.2, 2.3, 2.5_



- [x] 5.2 实现绘画画布组件





  - 创建DrawingCanvas.vue组件
  - 实现鼠标和触摸事件处理
  - 集成绘画工具状态和Canvas工具函数
  - 添加实时绘画预览功能


  - 编写组件测试
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 6.1, 6.4_

- [x] 5.3 实现擦除功能





  - 在DrawingCanvas组件中添加擦除模式
  - 实现擦除工具的Canvas操作
  - 确保擦除工具与其他工具的正确切换
  - 编写擦除功能的测试
  - _需求: 2.4_

- [x] 6. 创建绘画页面





- [x] 6.1 实现绘画页面布局


  - 创建DrawingPage.vue页面组件
  - 集成工具栏和画布组件
  - 实现响应式布局设计
  - 添加"make it swim"按钮
  - _需求: 2.6, 6.2, 6.3_



- [x] 6.2 实现鱼类命名对话框






  - 创建FishNamingDialog.vue组件
  - 添加名字输入和确认/取消功能
  - 集成到绘画页面的工作流程
  - 编写对话框组件测试
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [x] 7. 开发鱼类动画系统





- [x] 7.1 创建动画工具函数


  - 实现animationUtils.js动画工具库
  - 添加位置计算、边界检测等函数
  - 实现动画循环和性能优化
  - 编写动画工具函数测试
  - _需求: 4.3, 4.5_



- [x] 7.2 实现鱼类动画控制器





  - 创建fishAnimator.js动画控制器类
  - 实现鱼类移动和尾巴摆动逻辑
  - 添加边界碰撞和转向功能
  - 编写动画控制器测试


  - _需求: 4.2, 4.3, 4.5_

- [x] 7.3 创建游泳鱼类组件






  - 实现SwimmingFish.vue组件
  - 集成鱼类图像渲染和动画控制器
  - 添加鱼类名字显示功能
  - 编写组件测试
  - _需求: 4.1, 4.2, 4.4_

- [x] 8. 创建游泳页面




- [x] 8.1 实现鱼缸背景组件


  - 创建FishTank.vue背景组件
  - 添加水族箱视觉效果和装饰
  - 实现响应式背景设计
  - 编写背景组件测试
  - _需求: 4.1_


- [x] 8.2 实现游泳页面布局

  - 创建SwimmingPage.vue页面组件
  - 集成鱼缸背景和游泳鱼类组件
  - 添加返回绘画页面的导航
  - 实现页面的响应式设计
  - _需求: 4.6, 6.2, 6.3_

- [x] 9. 配置路由系统




- [x] 9.1 实现路由配置和导航



  - 配置Vue Router路由定义
  - 实现页面间的导航逻辑
  - 添加路由守卫保护游泳页面
  - 确保状态在路由切换时正确传递
  - 编写路由功能测试
  - _需求: 3.5, 5.1, 5.2, 5.4_

- [ ] 10. 实现数据持久化（可选）
- [ ] 10.1 创建API接口层
  - 实现fishApi.js API接口文件
  - 使用Axios配置HTTP请求
  - 添加鱼类数据的保存和加载功能
  - 实现错误处理和重试机制
  - 编写API接口测试
  - _需求: 5.5_

- [ ] 11. 响应式设计优化
- [ ] 11.1 实现移动端适配
  - 优化触摸设备的绘画体验
  - 调整工具栏在小屏幕上的布局
  - 确保所有功能在移动设备上可用
  - 测试不同屏幕尺寸的用户体验
  - _需求: 6.1, 6.2, 6.3, 6.4_

- [ ] 12. 集成测试和优化
- [ ] 12.1 实现端到端测试
  - 编写完整用户流程的E2E测试
  - 测试从绘画到游泳的完整体验
  - 验证跨页面状态传递的正确性
  - 测试错误场景和边界情况
  - _需求: 所有需求的集成验证_

- [ ] 12.2 性能优化和最终调试
  - 优化Canvas绘画性能
  - 确保动画流畅度达到60fps
  - 修复发现的bug和用户体验问题
  - 进行最终的代码审查和清理
  - _需求: 性能和用户体验优化_