<template>
  <div class="toolbar">
    <div class="toolbar-header">
      <h2 class="toolbar-title">绘画工具</h2>
    </div>
    
    <!-- Tool Selection Section -->
    <div class="toolbar-section">
      <h3 class="section-title">工具选择</h3>
      <div class="tool-buttons">
        <button
          class="tool-button"
          :class="{ active: currentTool === 'brush' }"
          @click="selectTool('brush')"
          title="画笔工具"
          aria-label="选择画笔工具"
        >
          <span class="tool-icon">🖌️</span>
          <span class="tool-label">画笔</span>
        </button>
        
        <button
          class="tool-button"
          :class="{ active: currentTool === 'eraser' }"
          @click="selectTool('eraser')"
          title="擦除工具"
          aria-label="选择擦除工具"
        >
          <span class="tool-icon">🧽</span>
          <span class="tool-label">擦除</span>
        </button>
      </div>
    </div>
    
    <!-- Color Picker Section -->
    <div class="toolbar-section">
      <h3 class="section-title">颜色选择</h3>
      <ColorPicker />
    </div>
    
    <!-- Size Slider Section -->
    <div class="toolbar-section">
      <h3 class="section-title">线条粗细</h3>
      <SizeSlider 
        :preview-color="currentTool === 'eraser' ? '#FFFFFF' : currentColor"
        @size-changed="onSizeChanged"
      />
    </div>
    
    <!-- Action Buttons Section -->
    <div class="toolbar-section">
      <h3 class="section-title">操作</h3>
      <div class="action-buttons">
        <button
          class="action-button"
          :class="{ disabled: !canUndo }"
          :disabled="!canUndo"
          @click="undoAction"
          title="撤销上一步操作"
          aria-label="撤销"
        >
          <span class="action-icon">↶</span>
          <span class="action-label">撤销</span>
        </button>
        
        <button
          class="action-button"
          :class="{ disabled: !canRedo }"
          :disabled="!canRedo"
          @click="redoAction"
          title="重做上一步操作"
          aria-label="重做"
        >
          <span class="action-icon">↷</span>
          <span class="action-label">重做</span>
        </button>
        
        <button
          class="action-button"
          :class="{ disabled: !hasContent }"
          :disabled="!hasContent"
          @click="clearCanvas"
          title="清空画布"
          aria-label="清空画布"
        >
          <span class="action-icon">🗑️</span>
          <span class="action-label">清除</span>
        </button>
        
        <button
          class="action-button"
          :class="{ disabled: !hasContent }"
          :disabled="!hasContent"
          @click="flipCanvas"
          title="水平翻转画布"
          aria-label="翻转画布"
        >
          <span class="action-icon">↔️</span>
          <span class="action-label">翻转</span>
        </button>
      </div>
    </div>
    
    <!-- Make It Swim Section -->
    <div class="toolbar-section">
      <button
        class="swim-button"
        :class="{ disabled: !hasContent }"
        :disabled="!hasContent"
        @click="makeItSwim"
        title="让你的鱼游起来！"
        aria-label="让鱼游起来"
      >
        <span class="swim-icon">🐠</span>
        <span class="swim-label">Make it Swim!</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, withDefaults } from 'vue'
import { useDrawingStore } from '@/stores/drawingStore'
import ColorPicker from './ColorPicker.vue'
import SizeSlider from './SizeSlider.vue'
import type { DrawingTool } from '@/types'

// Props
interface Props {
  canUndo?: boolean
  canRedo?: boolean
  hasContent?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  canUndo: false,
  canRedo: false,
  hasContent: false
})

// Emits
interface Emits {
  'tool-changed': [tool: DrawingTool]
  'size-changed': [size: number]
  'undo': []
  'redo': []
  'clear': []
  'flip': []
  'make-swim': []
}

const emit = defineEmits<Emits>()

// Store
const drawingStore = useDrawingStore()

// Computed
const currentTool = computed(() => drawingStore.currentTool)
const currentColor = computed(() => drawingStore.currentColor)
const canUndo = computed(() => props.canUndo)
const canRedo = computed(() => props.canRedo)
const hasContent = computed(() => props.hasContent)

// Methods
function selectTool(tool: DrawingTool) {
  drawingStore.setTool(tool)
  emit('tool-changed', tool)
}

function onSizeChanged(size: number) {
  emit('size-changed', size)
}

function undoAction() {
  if (canUndo.value) {
    const imageData = drawingStore.undo()
    emit('undo')
    return imageData
  }
}

function redoAction() {
  if (canRedo.value) {
    const imageData = drawingStore.redo()
    emit('redo')
    return imageData
  }
}

function clearCanvas() {
  if (hasContent.value) {
    drawingStore.clearCanvas()
    emit('clear')
  }
}

function flipCanvas() {
  if (hasContent.value) {
    drawingStore.flipCanvas()
    emit('flip')
  }
}

function makeItSwim() {
  if (hasContent.value) {
    emit('make-swim')
  }
}
</script>

<style scoped>
.toolbar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 320px;
  max-width: 400px;
}

.toolbar-header {
  text-align: center;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 12px;
}

.toolbar-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #495057;
}

.toolbar-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 6px;
}

/* Tool Selection Styles */
.tool-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.tool-button:hover {
  border-color: #007bff;
  background: #f8f9fa;
  transform: translateY(-1px);
}

.tool-button.active {
  border-color: #007bff;
  background: #e3f2fd;
  color: #007bff;
}

.tool-icon {
  font-size: 24px;
}

.tool-label {
  font-weight: 500;
}

/* Action Buttons Styles */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 10px 8px;
  border: 2px solid #dee2e6;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.action-button:hover:not(.disabled) {
  border-color: #28a745;
  background: #f8f9fa;
  transform: translateY(-1px);
}

.action-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.action-icon {
  font-size: 18px;
}

.action-label {
  font-weight: 500;
}

/* Make It Swim Button Styles */
.swim-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  border: 3px solid #ff6b6b;
  border-radius: 12px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.swim-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

.swim-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #dee2e6;
  border-color: #dee2e6;
  color: #6c757d;
  box-shadow: none;
}

.swim-icon {
  font-size: 24px;
  animation: swim 2s ease-in-out infinite;
}

.swim-label {
  font-size: 16px;
}

@keyframes swim {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(4px); }
}

/* Focus styles for accessibility */
.tool-button:focus,
.action-button:focus,
.swim-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
  .toolbar {
    min-width: auto;
    max-width: none;
    padding: 16px;
    gap: 16px;
  }
  
  .toolbar-title {
    font-size: 18px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .tool-buttons {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    grid-template-columns: 1fr 1fr;
    gap: 6px;
  }
  
  .swim-button {
    padding: 14px 20px;
    font-size: 16px;
  }
  
  .swim-label {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .toolbar {
    padding: 12px;
    gap: 12px;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .action-button {
    flex-direction: row;
    justify-content: center;
    padding: 8px 12px;
  }
  
  .swim-button {
    flex-direction: column;
    padding: 12px 16px;
  }
}
</style>