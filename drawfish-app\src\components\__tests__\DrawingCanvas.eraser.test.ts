import { describe, it, expect, beforeEach, vi, type MockedFunction } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useDrawingStore } from '@/stores/drawingStore'
import { CanvasUtils } from '@/utils/canvasUtils'

// Mock CanvasUtils
vi.mock('@/utils/canvasUtils', () => ({
  CanvasUtils: {
    initializeCanvas: vi.fn(),
    startDrawing: vi.fn(),
    continueDrawing: vi.fn(),
    endDrawing: vi.fn(),
    eraseAt: vi.fn(),
    eraseAlongPath: vi.fn(),
    clearCanvas: vi.fn(),
    flipCanvas: vi.fn(),
    getImageData: vi.fn(),
    setImageData: vi.fn(),
    getCanvasBounds: vi.fn(),
    screenToCanvasCoordinates: vi.fn()
  },
  DrawingHistoryManager: vi.fn().mockImplementation(() => ({
    addToHistory: vi.fn(),
    undo: vi.fn(),
    redo: vi.fn(),
    canUndo: vi.fn(() => false),
    canRedo: vi.fn(() => false),
    clearHistory: vi.fn()
  })),
  DrawingPathRecorder: vi.fn().mockImplementation(() => ({
    startPath: vi.fn(),
    addPoint: vi.fn(),
    endPath: vi.fn(() => []),
    getCurrentPath: vi.fn(() => []),
    isCurrentlyRecording: vi.fn(() => false),
    clearCurrentPath: vi.fn()
  }))
}))

// Mock ImageData for testing environment
class MockImageData {
  width: number
  height: number
  data: Uint8ClampedArray

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
}

global.ImageData = MockImageData as any

describe('DrawingCanvas - Eraser Functionality', () => {
  let drawingStore: ReturnType<typeof useDrawingStore>
  let mockContext: any

  beforeEach(() => {
    // Setup Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
    drawingStore = useDrawingStore()

    // Mock canvas context
    mockContext = {
      lineCap: 'round',
      lineJoin: 'round',
      imageSmoothingEnabled: true,
      globalCompositeOperation: 'source-over'
    }

    // Setup CanvasUtils mocks
    ;(CanvasUtils.initializeCanvas as MockedFunction<any>).mockReturnValue(mockContext)
    ;(CanvasUtils.getCanvasBounds as MockedFunction<any>).mockReturnValue({
      width: 800,
      height: 600,
      top: 0,
      left: 0
    })
    ;(CanvasUtils.screenToCanvasCoordinates as MockedFunction<any>).mockReturnValue({
      x: 100,
      y: 100
    })
    ;(CanvasUtils.getImageData as MockedFunction<any>).mockReturnValue(new MockImageData(800, 600))

    vi.clearAllMocks()
  })

  describe('Eraser Tool Settings', () => {
    it('should set eraser tool correctly', () => {
      drawingStore.setTool('eraser')
      expect(drawingStore.currentTool).toBe('eraser')
    })

    it('should use white color for eraser tool', () => {
      drawingStore.setTool('eraser')
      drawingStore.setColor('#FF0000') // This should not affect eraser
      
      const settings = drawingStore.currentToolSettings
      expect(settings.color).toBe('#FFFFFF') // Eraser should always use white
    })

    it('should respect brush size for eraser', () => {
      drawingStore.setTool('eraser')
      drawingStore.setBrushSize(20)
      
      const settings = drawingStore.currentToolSettings
      expect(settings.size).toBe(20)
    })

    it('should maintain opacity for eraser', () => {
      drawingStore.setTool('eraser')
      
      const settings = drawingStore.currentToolSettings
      expect(settings.opacity).toBe(1)
    })
  })

  describe('Eraser Canvas Operations', () => {
    it('should call eraseAt method with correct parameters', () => {
      const point = { x: 100, y: 100, pressure: 1 }
      const size = 15
      
      CanvasUtils.eraseAt(mockContext, point, size)
      
      expect(CanvasUtils.eraseAt).toHaveBeenCalledWith(mockContext, point, size)
    })

    it('should handle eraser tool switching', () => {
      // Start with brush
      drawingStore.setTool('brush')
      expect(drawingStore.currentTool).toBe('brush')
      
      // Switch to eraser
      drawingStore.setTool('eraser')
      expect(drawingStore.currentTool).toBe('eraser')
      
      // Switch back to brush
      drawingStore.setTool('brush')
      expect(drawingStore.currentTool).toBe('brush')
    })

    it('should maintain different sizes for different tools', () => {
      // Set brush size
      drawingStore.setTool('brush')
      drawingStore.setBrushSize(5)
      expect(drawingStore.brushSize).toBe(5)
      
      // Switch to eraser and change size
      drawingStore.setTool('eraser')
      drawingStore.setBrushSize(20)
      expect(drawingStore.brushSize).toBe(20)
      
      // Size should persist when switching back
      drawingStore.setTool('brush')
      expect(drawingStore.brushSize).toBe(20) // Size is shared between tools
    })
  })

  describe('Eraser Drawing State Management', () => {
    it('should handle drawing state for eraser', () => {
      drawingStore.setTool('eraser')
      
      // Start erasing
      drawingStore.setDrawingState(true)
      expect(drawingStore.isDrawing).toBe(true)
      
      // Stop erasing
      drawingStore.setDrawingState(false)
      expect(drawingStore.isDrawing).toBe(false)
    })

    it('should handle eraser history correctly', () => {
      drawingStore.setTool('eraser')
      
      // Simulate erasing operation
      const imageData = new MockImageData(800, 600)
      CanvasUtils.getImageData(mockContext, { width: 800, height: 600, top: 0, left: 0 })
      
      expect(CanvasUtils.getImageData).toHaveBeenCalled()
    })
  })

  describe('Eraser Tool Validation', () => {
    it('should validate eraser size limits', () => {
      drawingStore.setTool('eraser')
      
      // Test minimum size
      drawingStore.setBrushSize(0)
      expect(drawingStore.brushSize).toBe(5) // Should not change to invalid size
      
      // Test maximum size
      drawingStore.setBrushSize(100)
      expect(drawingStore.brushSize).toBe(5) // Should not change to invalid size
      
      // Test valid size
      drawingStore.setBrushSize(25)
      expect(drawingStore.brushSize).toBe(25)
    })

    it('should handle invalid eraser operations gracefully', () => {
      const point = { x: -100, y: -100, pressure: 1 }
      const size = 10
      
      // Should not throw error even with invalid coordinates
      expect(() => {
        CanvasUtils.eraseAt(mockContext, point, size)
      }).not.toThrow()
    })
  })

  describe('Eraser Integration with Canvas Utils', () => {
    it('should use correct composite operation for erasing', () => {
      // The eraseAt method should handle composite operation internally
      const point = { x: 50, y: 50, pressure: 1 }
      const size = 10
      
      CanvasUtils.eraseAt(mockContext, point, size)
      
      expect(CanvasUtils.eraseAt).toHaveBeenCalledWith(mockContext, point, size)
    })

    it('should preserve canvas state after erasing', () => {
      const point = { x: 100, y: 100, pressure: 1 }
      const size = 15
      
      // Erase operation
      CanvasUtils.eraseAt(mockContext, point, size)
      
      // Canvas should still be accessible
      expect(mockContext).toBeDefined()
      expect(mockContext.globalCompositeOperation).toBe('source-over')
    })

    it('should support smooth erasing along paths', () => {
      const fromPoint = { x: 10, y: 10, pressure: 1 }
      const toPoint = { x: 50, y: 50, pressure: 1 }
      const size = 12
      
      CanvasUtils.eraseAlongPath(mockContext, fromPoint, toPoint, size)
      
      expect(CanvasUtils.eraseAlongPath).toHaveBeenCalledWith(mockContext, fromPoint, toPoint, size)
    })
  })

  describe('Eraser Performance', () => {
    it('should handle multiple erase operations efficiently', () => {
      drawingStore.setTool('eraser')
      
      const points = [
        { x: 10, y: 10, pressure: 1 },
        { x: 20, y: 20, pressure: 1 },
        { x: 30, y: 30, pressure: 1 },
        { x: 40, y: 40, pressure: 1 },
        { x: 50, y: 50, pressure: 1 }
      ]
      
      points.forEach(point => {
        CanvasUtils.eraseAt(mockContext, point, 10)
      })
      
      expect(CanvasUtils.eraseAt).toHaveBeenCalledTimes(5)
    })

    it('should handle rapid eraser movements', () => {
      drawingStore.setTool('eraser')
      drawingStore.setDrawingState(true)
      
      // Simulate rapid movements
      for (let i = 0; i < 100; i++) {
        const point = { x: i, y: i, pressure: 1 }
        CanvasUtils.eraseAt(mockContext, point, 5)
      }
      
      expect(CanvasUtils.eraseAt).toHaveBeenCalledTimes(100)
      expect(drawingStore.isDrawing).toBe(true)
    })
  })

  describe('Eraser Error Handling', () => {
    it('should handle null context gracefully', () => {
      const point = { x: 100, y: 100, pressure: 1 }
      const size = 10
      
      expect(() => {
        CanvasUtils.eraseAt(null as any, point, size)
      }).not.toThrow()
    })

    it('should handle invalid point data', () => {
      const invalidPoint = { x: NaN, y: NaN, pressure: 1 }
      const size = 10
      
      expect(() => {
        CanvasUtils.eraseAt(mockContext, invalidPoint, size)
      }).not.toThrow()
    })

    it('should handle invalid size values', () => {
      const point = { x: 100, y: 100, pressure: 1 }
      
      expect(() => {
        CanvasUtils.eraseAt(mockContext, point, -5) // Negative size
        CanvasUtils.eraseAt(mockContext, point, 0)  // Zero size
        CanvasUtils.eraseAt(mockContext, point, NaN) // NaN size
      }).not.toThrow()
    })
  })
})