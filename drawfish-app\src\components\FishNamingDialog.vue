<template>
  <div class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-container" @click.stop>
      <!-- Dialog Header -->
      <header class="dialog-header">
        <h2 class="dialog-title">🐠 给你的鱼起个名字</h2>
        <button 
          class="close-button"
          @click="handleCancel"
          aria-label="关闭对话框"
        >
          ✕
        </button>
      </header>

      <!-- Fish Preview -->
      <div class="fish-preview-section">
        <div class="preview-container">
          <canvas
            ref="previewCanvasRef"
            class="fish-preview-canvas"
            :width="previewWidth"
            :height="previewHeight"
          />
        </div>
        <p class="preview-text">你的鱼看起来很棒！</p>
      </div>

      <!-- Name Input Section -->
      <div class="name-input-section">
        <label for="fish-name" class="input-label">鱼的名字:</label>
        <input
          id="fish-name"
          ref="nameInputRef"
          v-model="fishName"
          type="text"
          class="name-input"
          :class="{ error: hasError }"
          placeholder="给你的鱼起个好听的名字..."
          maxlength="50"
          @keyup.enter="handleConfirm"
          @input="clearError"
        />
        <div class="input-info">
          <span class="char-count" :class="{ warning: fishName.length > 40 }">
            {{ fishName.length }}/50
          </span>
          <span v-if="hasError" class="error-message">{{ errorMessage }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <footer class="dialog-actions">
        <button
          class="action-button cancel-button"
          @click="handleCancel"
        >
          取消
        </button>
        <button
          class="action-button confirm-button"
          :class="{ disabled: !canConfirm }"
          :disabled="!canConfirm"
          @click="handleConfirm"
        >
          <span class="button-icon">🌊</span>
          让它游起来！
        </button>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'

// Props
interface Props {
  fishImage: ImageData | null
}

const props = defineProps<Props>()

// Emits
interface Emits {
  confirm: [name: string]
  cancel: []
}

const emit = defineEmits<Emits>()

// Refs
const nameInputRef = ref<HTMLInputElement>()
const previewCanvasRef = ref<HTMLCanvasElement>()

// State
const fishName = ref('')
const hasError = ref(false)
const errorMessage = ref('')

// Preview canvas dimensions
const previewWidth = 300
const previewHeight = 200

// Computed
const canConfirm = computed(() => {
  const trimmedName = fishName.value.trim()
  return trimmedName.length > 0 && trimmedName.length <= 50 && !hasError.value
})

// Methods
const validateName = (name: string): { valid: boolean; message: string } => {
  const trimmed = name.trim()
  
  if (trimmed.length === 0) {
    return { valid: false, message: '请输入鱼的名字' }
  }
  
  if (trimmed.length > 50) {
    return { valid: false, message: '名字太长了，最多50个字符' }
  }
  
  // Check for invalid characters (optional)
  const invalidChars = /[<>"/\\|?*]/
  if (invalidChars.test(trimmed)) {
    return { valid: false, message: '名字包含无效字符' }
  }
  
  return { valid: true, message: '' }
}

const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

const handleConfirm = () => {
  const validation = validateName(fishName.value)
  
  if (!validation.valid) {
    hasError.value = true
    errorMessage.value = validation.message
    nameInputRef.value?.focus()
    return
  }
  
  emit('confirm', fishName.value.trim())
}

const handleCancel = () => {
  emit('cancel')
}

const handleOverlayClick = () => {
  // Allow closing by clicking overlay
  handleCancel()
}

const drawFishPreview = async () => {
  await nextTick()
  
  if (!previewCanvasRef.value || !props.fishImage) {
    return
  }
  
  const canvas = previewCanvasRef.value
  const ctx = canvas.getContext('2d')
  
  if (!ctx) {
    console.error('Failed to get canvas context for fish preview')
    return
  }
  
  try {
    // Clear canvas
    ctx.clearRect(0, 0, previewWidth, previewHeight)
    
    // Set background
    ctx.fillStyle = '#f0f8ff'
    ctx.fillRect(0, 0, previewWidth, previewHeight)
    
    // Create temporary canvas to draw the fish image
    const tempCanvas = document.createElement('canvas')
    const tempCtx = tempCanvas.getContext('2d')
    
    if (!tempCtx) {
      throw new Error('Failed to create temporary canvas context')
    }
    
    // Set temp canvas size to match image data
    tempCanvas.width = props.fishImage.width
    tempCanvas.height = props.fishImage.height
    
    // Draw the image data to temp canvas
    tempCtx.putImageData(props.fishImage, 0, 0)
    
    // Calculate scaling to fit preview canvas
    const scaleX = (previewWidth - 40) / props.fishImage.width
    const scaleY = (previewHeight - 40) / props.fishImage.height
    const scale = Math.min(scaleX, scaleY, 1) // Don't scale up
    
    const scaledWidth = props.fishImage.width * scale
    const scaledHeight = props.fishImage.height * scale
    
    // Center the image
    const x = (previewWidth - scaledWidth) / 2
    const y = (previewHeight - scaledHeight) / 2
    
    // Draw the fish image scaled and centered
    ctx.drawImage(tempCanvas, x, y, scaledWidth, scaledHeight)
    
    // Add a subtle border
    ctx.strokeStyle = '#ddd'
    ctx.lineWidth = 1
    ctx.strokeRect(x - 1, y - 1, scaledWidth + 2, scaledHeight + 2)
    
  } catch (error) {
    console.error('Failed to draw fish preview:', error)
    
    // Draw error placeholder
    ctx.fillStyle = '#f5f5f5'
    ctx.fillRect(0, 0, previewWidth, previewHeight)
    
    ctx.fillStyle = '#999'
    ctx.font = '16px sans-serif'
    ctx.textAlign = 'center'
    ctx.fillText('预览不可用', previewWidth / 2, previewHeight / 2)
  }
}

// Watch for fish image changes
watch(() => props.fishImage, () => {
  drawFishPreview()
}, { immediate: true })

// Lifecycle
onMounted(async () => {
  // Focus the input field
  await nextTick()
  nameInputRef.value?.focus()
  
  // Draw the fish preview
  drawFishPreview()
})
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(4px);
}

.dialog-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: dialogEnter 0.3s ease-out;
}

@keyframes dialogEnter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dialog Header */
.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e9ecef;
}

.dialog-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: #f8f9fa;
  color: #495057;
}

/* Fish Preview Section */
.fish-preview-section {
  padding: 20px 24px;
  text-align: center;
}

.preview-container {
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
}

.fish-preview-canvas {
  display: block;
  background: #f0f8ff;
}

.preview-text {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  font-style: italic;
}

/* Name Input Section */
.name-input-section {
  padding: 0 24px 20px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
  font-size: 1rem;
}

.name-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.name-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.name-input.error {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.input-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
  min-height: 20px;
}

.char-count {
  font-size: 0.8rem;
  color: #6c757d;
  font-family: monospace;
}

.char-count.warning {
  color: #fd7e14;
  font-weight: 500;
}

.error-message {
  font-size: 0.8rem;
  color: #dc3545;
  font-weight: 500;
}

/* Dialog Actions */
.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px 24px;
  border-top: 1px solid #e9ecef;
}

.action-button {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.cancel-button {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.cancel-button:hover {
  background: #e9ecef;
  color: #495057;
}

.confirm-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.confirm-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.confirm-button.disabled {
  background: #dee2e6;
  color: #6c757d;
  cursor: not-allowed;
  box-shadow: none;
}

.button-icon {
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 600px) {
  .dialog-overlay {
    padding: 10px;
  }
  
  .dialog-container {
    max-width: none;
    margin: 0;
  }
  
  .dialog-header {
    padding: 20px 20px 16px;
  }
  
  .dialog-title {
    font-size: 1.3rem;
  }
  
  .fish-preview-section {
    padding: 16px 20px;
  }
  
  .name-input-section {
    padding: 0 20px 16px;
  }
  
  .dialog-actions {
    padding: 16px 20px 20px;
    flex-direction: column;
  }
  
  .action-button {
    padding: 14px 20px;
  }
}

@media (max-width: 400px) {
  .preview-container {
    transform: scale(0.8);
  }
  
  .dialog-title {
    font-size: 1.2rem;
  }
}

/* Focus styles for accessibility */
.action-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.close-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .dialog-container {
    animation: none;
  }
  
  .confirm-button:hover:not(.disabled) {
    transform: none;
  }
}
</style>