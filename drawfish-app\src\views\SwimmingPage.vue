<template>
  <div class="swimming-page">
    <!-- 页面标题和导航 -->
    <header class="page-header">
      <h1 class="page-title">{{ pageTitle }}</h1>
      <nav class="page-nav">
        <button @click="goBackToDrawing" class="nav-button back-button">
          <span class="button-icon">←</span>
          返回绘画页面
        </button>
        <button v-if="hasFish" @click="toggleAnimation" class="nav-button animation-button">
          <span class="button-icon">{{ isAnimating ? '⏸️' : '▶️' }}</span>
          {{ isAnimating ? '暂停游泳' : '开始游泳' }}
        </button>
        <button v-if="hasFish" @click="testMovement" class="nav-button test-button">
          <span class="button-icon">🧪</span>
          测试移动
        </button>
      </nav>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 鱼缸容器 -->
      <div class="tank-container" ref="tankContainer">
        <FishTank 
          :width="tankDimensions.width" 
          :height="tankDimensions.height"
          class="fish-tank"
        >
          <!-- 游泳的鱼轮廓 -->
          <FishOutline
            v-if="currentFish"
            :fish-data="currentFish"
            :bounds="fishBounds"
            :auto-start="autoStartAnimation"
            :show-name="true"
            :interactive="true"
            @update:fish-data="handleFishUpdate"
            @fish-click="handleFishClick"
            @animation-start="handleAnimationStart"
            @animation-stop="handleAnimationStop"
            ref="swimmingFishRef"
          />
          
          <!-- 无鱼状态提示 -->
          <div v-else class="no-fish-message">
            <div class="message-content">
              <div class="message-icon">🐠</div>
              <h2>还没有鱼在游泳</h2>
              <p>请先回到绘画页面创建一条鱼</p>
              <button @click="goBackToDrawing" class="create-fish-button">
                去画鱼
              </button>
            </div>
          </div>
        </FishTank>
      </div>

      <!-- 鱼类信息面板 -->
      <aside v-if="currentFish" class="fish-info-panel">
        <div class="fish-info-content">
          <h3>鱼类信息</h3>
          <div class="fish-details">
            <div class="detail-item">
              <span class="detail-label">名字:</span>
              <span class="detail-value">{{ currentFish.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">创建时间:</span>
              <span class="detail-value">{{ formatDate(currentFish.createdAt) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">尺寸:</span>
              <span class="detail-value">{{ currentFish.dimensions.width }} × {{ currentFish.dimensions.height }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">状态:</span>
              <span class="detail-value status" :class="{ active: isAnimating }">
                {{ isAnimating ? '游泳中' : '静止' }}
              </span>
            </div>
          </div>
        </div>
      </aside>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useFishStore } from '@/stores/fishStore'
import FishTank from '@/components/FishTank.vue'
import FishOutline from '@/components/FishOutline.vue'
import type { FishData, CanvasBounds } from '@/types'

// Router
const router = useRouter()

// Store
const fishStore = useFishStore()

// Template refs
const tankContainer = ref<HTMLDivElement>()
const swimmingFishRef = ref<InstanceType<typeof FishOutline>>()

// Component state
const isAnimating = ref(false)
const autoStartAnimation = ref(true)
const tankDimensions = ref({
  width: 800,
  height: 600
})

// Computed properties
const currentFish = computed(() => fishStore.currentFish)
const hasFish = computed(() => fishStore.hasFish)

const pageTitle = computed(() => {
  if (currentFish.value) {
    return `${currentFish.value.name} 在游泳!`
  }
  return '观看你的鱼游泳!'
})

const fishBounds = computed((): CanvasBounds => ({
  width: tankDimensions.value.width - 40, // 留出边距
  height: tankDimensions.value.height - 100, // 留出沙子底部和顶部空间
  offsetX: 20,
  offsetY: 20
}))

// Methods
const goBackToDrawing = (): void => {
  router.push('/')
}

const toggleAnimation = (): void => {
  if (!swimmingFishRef.value) return

  if (isAnimating.value) {
    swimmingFishRef.value.pauseAnimation()
  } else {
    swimmingFishRef.value.resumeAnimation()
  }
}

const testMovement = (): void => {
  if (!currentFish.value || !swimmingFishRef.value) return

  console.log('Testing fish movement...')
  console.log('Current fish position:', currentFish.value.position)
  console.log('Current fish velocity:', currentFish.value.velocity)

  // Manually move the fish to test if position updates work
  const newPosition = {
    x: currentFish.value.position.x + 50,
    y: currentFish.value.position.y
  }

  const updatedFish = {
    ...currentFish.value,
    position: newPosition
  }

  console.log('Moving fish to:', newPosition)
  handleFishUpdate(updatedFish)
}

const handleFishUpdate = (updatedFishData: FishData): void => {
  // Debug: Log fish updates
  if (Math.random() < 0.05) { // Log ~5% of updates
    console.log('Swimming page received fish update:', {
      position: updatedFishData.position,
      velocity: updatedFishData.velocity
    })
  }
  fishStore.setCurrentFish(updatedFishData)
  fishStore.addToCollection(updatedFishData)
}

const handleFishClick = (fishData: FishData): void => {
  console.log('Fish clicked:', fishData.name)
  // 可以在这里添加点击鱼类的交互效果
}

const handleAnimationStart = (): void => {
  isAnimating.value = true
  fishStore.startSwimming()
}

const handleAnimationStop = (): void => {
  isAnimating.value = false
  fishStore.stopSwimming()
}

const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

// Responsive handling
const updateTankDimensions = (): void => {
  if (!tankContainer.value) return

  const container = tankContainer.value
  const containerRect = container.getBoundingClientRect()
  const maxWidth = Math.min(containerRect.width - 40, 1000)
  const maxHeight = Math.min(window.innerHeight - 200, 700)

  // 保持宽高比
  const aspectRatio = 4 / 3
  let newWidth = maxWidth
  let newHeight = newWidth / aspectRatio

  if (newHeight > maxHeight) {
    newHeight = maxHeight
    newWidth = newHeight * aspectRatio
  }

  tankDimensions.value = {
    width: Math.max(newWidth, 400), // 最小宽度
    height: Math.max(newHeight, 300) // 最小高度
  }

  // 更新鱼店的游泳边界
  fishStore.setSwimBounds(tankDimensions.value)
}

// Lifecycle
onMounted(async () => {
  await nextTick()
  
  // 如果没有鱼，可能需要从路由参数或其他地方获取
  if (!currentFish.value) {
    console.warn('No fish data available for swimming page')
  }

  // 设置初始尺寸
  updateTankDimensions()

  // 监听窗口大小变化
  window.addEventListener('resize', updateTankDimensions)

  // 开始游泳动画
  if (currentFish.value && autoStartAnimation.value) {
    await nextTick()
    if (swimmingFishRef.value) {
      swimmingFishRef.value.startAnimation()
    }
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', updateTankDimensions)
  
  // 停止游泳状态
  fishStore.stopSwimming()
})
</script>

<style scoped>
.swimming-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.page-title {
  color: white;
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-nav {
  display: flex;
  gap: 12px;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-button:active {
  transform: translateY(0);
}

.back-button {
  color: #666;
}

.animation-button {
  color: #4A90E2;
}

.button-icon {
  font-size: 16px;
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.tank-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.fish-tank {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border-radius: 12px;
}

/* 无鱼状态 */
.no-fish-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.message-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 40px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.message-icon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.message-content h2 {
  margin: 0 0 12px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.message-content p {
  margin: 0 0 24px 0;
  opacity: 0.8;
}

.create-fish-button {
  padding: 12px 24px;
  background: #4A90E2;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.create-fish-button:hover {
  background: #357ABD;
  transform: translateY(-2px);
}

/* 鱼类信息面板 */
.fish-info-panel {
  width: 280px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.fish-info-content h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.fish-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.detail-value {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-align: right;
}

.detail-value.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #f0f0f0;
  color: #666;
}

.detail-value.status.active {
  background: #e8f5e8;
  color: #2d8f2d;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }
  
  .fish-info-panel {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .swimming-page {
    padding: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .page-title {
    font-size: 1.5rem;
  }
  
  .page-nav {
    justify-content: center;
  }
  
  .nav-button {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .tank-container {
    min-height: 300px;
  }
  
  .message-content {
    padding: 30px 20px;
  }
  
  .message-icon {
    font-size: 3rem;
  }
  
  .message-content h2 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .swimming-page {
    padding: 10px;
  }
  
  .page-title {
    font-size: 1.3rem;
  }
  
  .nav-button {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .button-icon {
    font-size: 14px;
  }
  
  .fish-info-panel {
    padding: 15px;
  }
  
  .message-content {
    padding: 20px 15px;
  }
  
  .message-icon {
    font-size: 2.5rem;
  }
  
  .message-content h2 {
    font-size: 1.2rem;
  }
  
  .create-fish-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
</style>