import { describe, it, expect, beforeEach } from 'vitest'
import { OutlineExtractor, type FishOutline } from '../outlineExtractor'

// Mock ImageData for testing
class MockImageData {
  width: number
  height: number
  data: Uint8ClampedArray

  constructor(width: number, height: number) {
    this.width = width
    this.height = height
    this.data = new Uint8ClampedArray(width * height * 4)
  }
}

global.ImageData = MockImageData as any

// Mock Path2D for testing
global.Path2D = class Path2D {
  private commands: string[] = []

  moveTo(x: number, y: number) {
    this.commands.push(`M ${x} ${y}`)
  }

  lineTo(x: number, y: number) {
    this.commands.push(`L ${x} ${y}`)
  }

  closePath() {
    this.commands.push('Z')
  }

  toString() {
    return this.commands.join(' ')
  }
} as any

describe('OutlineExtractor', () => {
  let imageData: ImageData

  beforeEach(() => {
    imageData = new ImageData(20, 20)
  })

  describe('extractOutlines', () => {
    it('should return empty array for transparent image', () => {
      const outlines = OutlineExtractor.extractOutlines(imageData)
      expect(outlines).toEqual([])
    })

    it('should extract outline from simple shape', () => {
      // Create a simple 3x3 red square in the center
      const centerX = 10
      const centerY = 10
      
      for (let y = centerY - 1; y <= centerY + 1; y++) {
        for (let x = centerX - 1; x <= centerX + 1; x++) {
          const index = (y * imageData.width + x) * 4
          imageData.data[index] = 255     // R
          imageData.data[index + 1] = 0   // G
          imageData.data[index + 2] = 0   // B
          imageData.data[index + 3] = 255 // A
        }
      }

      const outlines = OutlineExtractor.extractOutlines(imageData)
      
      console.log('Extracted outlines:', outlines.length)
      if (outlines.length > 0) {
        console.log('First outline:', outlines[0])
      }
      
      expect(outlines.length).toBeGreaterThan(0)
      expect(outlines[0].color).toBe('rgb(255,0,0)')
      expect(outlines[0].points.length).toBeGreaterThan(3)
      expect(outlines[0].thickness).toBeGreaterThan(0)
    })

    it('should extract multiple outlines from different colored shapes', () => {
      // Create a red square
      for (let y = 5; y <= 7; y++) {
        for (let x = 5; x <= 7; x++) {
          const index = (y * imageData.width + x) * 4
          imageData.data[index] = 255     // R
          imageData.data[index + 1] = 0   // G
          imageData.data[index + 2] = 0   // B
          imageData.data[index + 3] = 255 // A
        }
      }

      // Create a blue square
      for (let y = 12; y <= 14; y++) {
        for (let x = 12; x <= 14; x++) {
          const index = (y * imageData.width + x) * 4
          imageData.data[index] = 0       // R
          imageData.data[index + 1] = 0   // G
          imageData.data[index + 2] = 255 // B
          imageData.data[index + 3] = 255 // A
        }
      }

      const outlines = OutlineExtractor.extractOutlines(imageData)
      
      expect(outlines.length).toBe(2)
      
      const colors = outlines.map(o => o.color).sort()
      expect(colors).toEqual(['rgb(0,0,255)', 'rgb(255,0,0)'])
    })

    it('should ignore very small components', () => {
      // Create a single pixel
      const index = (10 * imageData.width + 10) * 4
      imageData.data[index] = 255     // R
      imageData.data[index + 1] = 0   // G
      imageData.data[index + 2] = 0   // B
      imageData.data[index + 3] = 255 // A

      const outlines = OutlineExtractor.extractOutlines(imageData)
      
      // Should ignore single pixels (component size <= 3)
      expect(outlines).toEqual([])
    })
  })

  describe('outlinesToSVGPaths', () => {
    it('should convert outlines to SVG path strings', () => {
      const outlines: FishOutline[] = [
        {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
            { x: 0, y: 10 }
          ],
          color: 'rgb(255,0,0)',
          thickness: 2
        }
      ]

      const svgPaths = OutlineExtractor.outlinesToSVGPaths(outlines)
      
      expect(svgPaths).toHaveLength(1)
      expect(svgPaths[0]).toBe('M 0 0 L 10 0 L 10 10 L 0 10 Z')
    })

    it('should handle empty outlines', () => {
      const outlines: FishOutline[] = [
        {
          points: [],
          color: 'rgb(255,0,0)',
          thickness: 2
        }
      ]

      const svgPaths = OutlineExtractor.outlinesToSVGPaths(outlines)
      
      expect(svgPaths).toHaveLength(1)
      expect(svgPaths[0]).toBe('')
    })
  })

  describe('outlinesToPaths', () => {
    it('should convert outlines to Path2D objects', () => {
      const outlines: FishOutline[] = [
        {
          points: [
            { x: 0, y: 0 },
            { x: 10, y: 0 },
            { x: 10, y: 10 },
            { x: 0, y: 10 }
          ],
          color: 'rgb(255,0,0)',
          thickness: 2
        }
      ]

      const paths = OutlineExtractor.outlinesToPaths(outlines)
      
      expect(paths).toHaveLength(1)
      expect(paths[0]).toBeInstanceOf(Path2D)
    })

    it('should handle empty outlines', () => {
      const outlines: FishOutline[] = [
        {
          points: [],
          color: 'rgb(255,0,0)',
          thickness: 2
        }
      ]

      const paths = OutlineExtractor.outlinesToPaths(outlines)
      
      expect(paths).toHaveLength(1)
      expect(paths[0]).toBeInstanceOf(Path2D)
    })
  })

  describe('Douglas-Peucker simplification', () => {
    it('should simplify complex outlines', () => {
      // Create a complex shape with many points
      const points = []
      for (let i = 0; i < 100; i++) {
        const angle = (i / 100) * Math.PI * 2
        points.push({
          x: Math.cos(angle) * 10 + 10,
          y: Math.sin(angle) * 10 + 10
        })
      }

      // Create image data with this circular shape
      const complexImageData = new ImageData(20, 20)
      for (const point of points) {
        const x = Math.round(point.x)
        const y = Math.round(point.y)
        if (x >= 0 && x < 20 && y >= 0 && y < 20) {
          const index = (y * 20 + x) * 4
          complexImageData.data[index] = 255     // R
          complexImageData.data[index + 1] = 0   // G
          complexImageData.data[index + 2] = 0   // B
          complexImageData.data[index + 3] = 255 // A
        }
      }

      const outlines = OutlineExtractor.extractOutlines(complexImageData)
      
      if (outlines.length > 0) {
        // The simplified outline should have fewer points than the original
        expect(outlines[0].points.length).toBeLessThan(points.length)
        expect(outlines[0].points.length).toBeGreaterThan(3)
      }
    })
  })

  describe('Color tolerance', () => {
    it('should group similar colors together', () => {
      // Create shapes with slightly different shades of red
      for (let y = 5; y <= 7; y++) {
        for (let x = 5; x <= 7; x++) {
          const index = (y * imageData.width + x) * 4
          imageData.data[index] = 255     // R
          imageData.data[index + 1] = 0   // G
          imageData.data[index + 2] = 0   // B
          imageData.data[index + 3] = 255 // A
        }
      }

      // Adjacent shape with slightly different red (within tolerance)
      for (let y = 5; y <= 7; y++) {
        for (let x = 8; x <= 10; x++) {
          const index = (y * imageData.width + x) * 4
          imageData.data[index] = 250     // Slightly different R
          imageData.data[index + 1] = 5   // Slightly different G
          imageData.data[index + 2] = 5   // Slightly different B
          imageData.data[index + 3] = 255 // A
        }
      }

      const outlines = OutlineExtractor.extractOutlines(imageData)
      
      // Should be treated as one connected component due to color tolerance
      expect(outlines.length).toBeLessThanOrEqual(2)
    })
  })
})